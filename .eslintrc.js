// module.exports = {
//   rules: {
//     "vue/multi-word-component-names": "off",
//   },
// };
module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es6: true, // 启用ES6语法支持
  },
  parser: 'vue-eslint-parser', // 必须指定Vue解析器
  parserOptions: {
    parser: '@babel/eslint-parser', // 使用Babel解析器处理JS
    ecmaVersion: 2020, // 支持最新ECMAScript特性
    sourceType: 'module', // 允许使用import/export
  },
  plugins: ['vue', 'prettier'],
  extends: [
    'plugin:vue/recommended', // 使用Vue官方推荐规则
    'eslint:recommended',
    'plugin:prettier/recommended',
  ],
  rules: {
    // 自定义规则（可选）
    'vue/multi-word-component-names': 'off',
    'no-prototype-builtins': 'off',
    'no-unused-vars': [
      2,
      {
        vars: 'all',
        args: 'none',
      },
    ],
    'prettier/prettier': 'error',
  },
};
