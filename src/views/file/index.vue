<template>
  <div class="file-manager">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>

    <!-- 头部导航栏 -->
    <div class="header">
      <div class="header-left">
        <div class="logo">
          <i class="el-icon-folder-opened"></i>
          <span>文件管理器</span>
        </div>
        <div class="nav-breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="(path, index) in breadcrumbPath"
              :key="index"
              :class="{ clickable: index < breadcrumbPath.length - 1 }"
              @click.native="navigateToPath(index)"
            >
              <i v-if="index === 0" class="el-icon-house"></i>
              {{ path }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <div class="toolbar">
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索文件..."
            prefix-icon="el-icon-search"
            size="small"
            class="search-input"
            @input="handleSearch"
          ></el-input>
        </div>
        <el-button
          type="primary"
          icon="el-icon-upload2"
          class="glass-button primary"
          @click="handleImportFile"
        >
          导入文件
        </el-button>
        <el-button
          type="success"
          icon="el-icon-folder-add"
          class="glass-button success"
          @click="showCreateFolderDialog = true"
        >
          新建文件夹
        </el-button>
        <el-button
          :type="viewMode === 'grid' ? 'primary' : 'default'"
          :icon="viewMode === 'grid' ? 'el-icon-menu' : 'el-icon-s-grid'"
          class="glass-button toggle"
          @click="toggleViewMode"
        >
          {{ viewMode === 'grid' ? '列表' : '网格' }}
        </el-button>
      </div>
    </div>

    <!-- 统计信息栏 -->
    <div class="stats-bar">
      <div class="stat-item">
        <i class="el-icon-document"></i>
        <span>{{ fileStats.total }} 个项目</span>
      </div>
      <div class="stat-item">
        <i class="el-icon-folder"></i>
        <span>{{ fileStats.folders }} 个文件夹</span>
      </div>
      <div class="stat-item">
        <i class="el-icon-tickets"></i>
        <span>{{ fileStats.files }} 个文件</span>
      </div>
      <div class="stat-item">
        <i class="el-icon-pie-chart"></i>
        <span>{{ formatFileSize(fileStats.totalSize) }}</span>
      </div>
    </div>

    <!-- 文件列表区域 -->
    <div class="file-list-container">
      <!-- 加载动画 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="loading-text">加载中...</div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="currentFiles.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="el-icon-folder-opened"></i>
        </div>
        <div class="empty-text">此文件夹为空</div>
        <div class="empty-actions">
          <el-button type="primary" class="glass-button" @click="handleImportFile">
            导入文件
          </el-button>
          <el-button class="glass-button" @click="showCreateFolderDialog = true">
            创建文件夹
          </el-button>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-else-if="viewMode === 'grid'" class="grid-view">
        <div
          v-for="item in filteredFiles"
          :key="item.id"
          class="file-item"
          :class="{
            selected: selectedItem?.id === item.id,
            'is-folder': item.type === 'folder',
          }"
          @dblclick="handleItemDoubleClick(item)"
          @click="selectItem(item)"
          @contextmenu.prevent="showContextMenu($event, item)"
        >
          <div class="file-icon-container">
            <div class="file-icon">
              <i :class="getFileIcon(item)" :style="{ color: getFileColor(item) }"></i>
              <div v-if="item.type !== 'folder'" class="file-type-badge">
                {{ getFileExtension(item.name) }}
              </div>
            </div>
          </div>
          <div class="file-name" :title="item.name">{{ item.name }}</div>
          <div class="file-info">
            <span class="file-size">{{ formatFileSize(item.size) }}</span>
            <span class="file-date">{{ formatDate(item.modifiedAt) }}</span>
          </div>
          <div class="file-actions">
            <el-button
              v-if="item.type !== 'folder'"
              type="text"
              class="action-btn"
              title="预览"
              @click.stop="openFilePreview(item)"
            >
              <i class="el-icon-view"></i>
            </el-button>
            <el-button
              type="text"
              class="action-btn danger"
              title="删除"
              @click.stop="deleteFile(item)"
            >
              <i class="el-icon-delete"></i>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="list-view">
        <el-table
          :data="filteredFiles"
          class="glass-table"
          :row-class-name="getRowClassName"
          empty-text="暂无文件"
          @row-dblclick="handleItemDoubleClick"
          @row-click="selectItem"
          @row-contextmenu="showContextMenu"
        >
          <el-table-column width="60">
            <template slot-scope="scope">
              <div class="table-icon">
                <i :class="getFileIcon(scope.row)" :style="{ color: getFileColor(scope.row) }"></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="名称" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="file-name-cell">
                <span>{{ scope.row.name }}</span>
                <el-tag v-if="scope.row.type !== 'folder'" size="mini" class="file-type-tag">
                  {{ getFileExtension(scope.row.name) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="100" sortable>
            <template slot-scope="scope">
              <span class="file-size-cell">{{ formatFileSize(scope.row.size) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="modifiedAt" label="修改时间" width="160" sortable>
            <template slot-scope="scope">
              <span class="file-date-cell">{{ formatDate(scope.row.modifiedAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template slot-scope="scope">
              <div class="table-actions">
                <el-button
                  v-if="scope.row.type !== 'folder'"
                  type="text"
                  class="action-button"
                  title="预览"
                  @click="openFilePreview(scope.row)"
                >
                  <i class="el-icon-view"></i>
                </el-button>
                <el-button
                  type="text"
                  class="action-button danger"
                  title="删除"
                  @click="deleteFile(scope.row)"
                >
                  <i class="el-icon-delete"></i>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 创建文件夹对话框 -->
    <el-dialog
      title="新建文件夹"
      :visible.sync="showCreateFolderDialog"
      width="450px"
      class="glass-dialog create-folder-dialog"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <div class="dialog-icon">
          <i class="el-icon-folder-add"></i>
        </div>
        <el-form ref="folderForm" :model="folderForm" :rules="folderRules">
          <el-form-item label="文件夹名称" prop="name">
            <el-input
              v-model="folderForm.name"
              placeholder="请输入文件夹名称"
              maxlength="100"
              show-word-limit
              @keyup.enter.native="createFolder"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <el-button class="glass-button" @click="cancelCreateFolder">取消</el-button>
        <el-button type="primary" class="glass-button primary" @click="createFolder"
          >确定</el-button
        >
      </span>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog
      :title="`预览 - ${previewFile?.name || ''}`"
      :visible.sync="showPreviewDialog"
      width="80%"
      class="glass-dialog preview-dialog"
      :close-on-click-modal="false"
      @closed="handlePreviewDialogClosed"
    >
      <div class="preview-content">
        <div class="preview-header">
          <div class="file-info-detail">
            <div class="file-icon-large">
              <i
                v-if="previewFile"
                :class="getFileIcon(previewFile)"
                :style="{ color: getFileColor(previewFile) }"
              ></i>
            </div>
            <div class="file-details">
              <h3>{{ previewFile?.name || '' }}</h3>
              <p>大小: {{ formatFileSize(previewFile?.size) }}</p>
              <p>修改时间: {{ formatDate(previewFile?.modifiedAt) }}</p>
            </div>
          </div>
        </div>

        <!-- 图片预览 -->
        <div v-if="previewType === 'image'" class="image-preview">
          <div class="image-container">
            <img :src="previewContent" :alt="previewFile?.name || '图片'" />
          </div>
        </div>

        <!-- 文本预览 -->
        <div v-else-if="previewType === 'text'" class="text-preview">
          <div class="text-container">
            <pre>{{ previewContent }}</pre>
          </div>
        </div>

        <!-- PDF预览 -->
        <div v-else-if="previewType === 'pdf'" class="pdf-preview">
          <iframe :src="previewContent" width="100%" height="600px"></iframe>
        </div>

        <!-- 不支持预览 -->
        <div v-else class="unsupported-preview">
          <div class="unsupported-icon">
            <i class="el-icon-warning-outline"></i>
          </div>
          <h3>暂不支持预览</h3>
          <p>此文件类型暂不支持在线预览</p>
          <el-button type="primary" class="glass-button">下载文件</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 右键菜单 -->
    <div v-show="showContextMenuFlag" class="context-menu" :style="contextMenuStyle" @click.stop>
      <div
        v-if="contextMenuItem?.type !== 'folder'"
        class="menu-item"
        @click="handleMenuAction('preview')"
      >
        <i class="el-icon-view"></i>
        <span>预览</span>
      </div>
      <div class="menu-item" @click="handleMenuAction('rename')">
        <i class="el-icon-edit"></i>
        <span>重命名</span>
      </div>
      <div class="menu-item" @click="handleMenuAction('copy')">
        <i class="el-icon-document-copy"></i>
        <span>复制</span>
      </div>
      <div class="menu-item danger" @click="handleMenuAction('delete')">
        <i class="el-icon-delete"></i>
        <span>删除</span>
      </div>
    </div>

    <!-- 隐藏的文件上传input -->
    <input ref="fileInput" type="file" multiple style="display: none" @change="handleFileUpload" />
  </div>
</template>

<script>
export default {
  name: 'FileManager',
  data() {
    return {
      viewMode: 'grid', // 'grid' 或 'list'
      currentPath: ['根目录'],
      selectedItem: null,
      showCreateFolderDialog: false,
      showPreviewDialog: false,
      previewContent: '',
      previewType: '',
      previewFile: null,
      loading: false,
      searchQuery: '',

      // 文件夹表单
      folderForm: {
        name: '',
      },
      folderRules: {
        name: [
          { required: true, message: '请输入文件夹名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
          { pattern: /^[^\\/:*?"<>|]+$/, message: '文件夹名称不能包含特殊字符', trigger: 'blur' },
        ],
      },

      // 右键菜单
      showContextMenuFlag: false,
      contextMenuStyle: {},
      contextMenuItem: null,

      // 假数据
      fileSystem: {
        '/': [
          {
            id: 1,
            name: '工作文档',
            type: 'folder',
            size: 0,
            modifiedAt: new Date('2024-01-15'),
            children: [
              {
                id: 11,
                name: '年度工作总结.docx',
                type: 'document',
                size: 245760,
                modifiedAt: new Date('2024-01-14'),
              },
              {
                id: 12,
                name: '项目规划书.pdf',
                type: 'pdf',
                size: 512000,
                modifiedAt: new Date('2024-01-13'),
              },
              {
                id: 13,
                name: '会议纪要.txt',
                type: 'text',
                size: 2048,
                modifiedAt: new Date('2024-01-12'),
              },
              {
                id: 14,
                name: '数据分析报告.xlsx',
                type: 'excel',
                size: 87432,
                modifiedAt: new Date('2024-01-11'),
              },
            ],
          },
          {
            id: 2,
            name: '设计素材',
            type: 'folder',
            size: 0,
            modifiedAt: new Date('2024-01-10'),
            children: [
              {
                id: 21,
                name: 'banner设计.jpg',
                type: 'image',
                size: 1024000,
                modifiedAt: new Date('2024-01-09'),
              },
              {
                id: 22,
                name: 'logo设计.png',
                type: 'image',
                size: 2048000,
                modifiedAt: new Date('2024-01-08'),
              },
              {
                id: 23,
                name: '产品原型图.sketch',
                type: 'design',
                size: 5120000,
                modifiedAt: new Date('2024-01-07'),
              },
            ],
          },
          {
            id: 3,
            name: '媒体文件',
            type: 'folder',
            size: 0,
            modifiedAt: new Date('2024-01-05'),
            children: [
              {
                id: 31,
                name: '产品演示.mp4',
                type: 'video',
                size: 52428800,
                modifiedAt: new Date('2024-01-04'),
              },
              {
                id: 32,
                name: '背景音乐.mp3',
                type: 'audio',
                size: 4567890,
                modifiedAt: new Date('2024-01-03'),
              },
            ],
          },
          {
            id: 4,
            name: '开发项目',
            type: 'folder',
            size: 0,
            modifiedAt: new Date('2024-01-02'),
            children: [
              {
                id: 41,
                name: 'main.js',
                type: 'code',
                size: 15678,
                modifiedAt: new Date('2024-01-01'),
              },
              {
                id: 42,
                name: 'package.json',
                type: 'json',
                size: 2345,
                modifiedAt: new Date('2023-12-31'),
              },
            ],
          },
          {
            id: 5,
            name: '重要合同.docx',
            type: 'document',
            size: 102400,
            modifiedAt: new Date('2024-01-02'),
          },
          {
            id: 6,
            name: 'README.md',
            type: 'text',
            size: 4096,
            modifiedAt: new Date('2024-01-01'),
          },
          {
            id: 7,
            name: '财务报表.xlsx',
            type: 'excel',
            size: 256000,
            modifiedAt: new Date('2023-12-30'),
          },
        ],
      },
    };
  },

  computed: {
    breadcrumbPath() {
      return this.currentPath;
    },

    currentFiles() {
      let files = this.fileSystem['/'];

      // 如果不在根目录，需要遍历到当前目录
      for (let i = 1; i < this.currentPath.length; i++) {
        const folderName = this.currentPath[i];
        const folder = files.find((item) => item.name === folderName && item.type === 'folder');
        if (folder && folder.children) {
          files = folder.children;
        }
      }

      return files || [];
    },

    filteredFiles() {
      if (!this.searchQuery.trim()) {
        return this.currentFiles;
      }

      const query = this.searchQuery.toLowerCase();
      return this.currentFiles.filter((file) => file.name.toLowerCase().includes(query));
    },

    fileStats() {
      const files = this.currentFiles;
      const folders = files.filter((item) => item.type === 'folder').length;
      const regularFiles = files.filter((item) => item.type !== 'folder').length;
      const totalSize = files.reduce((sum, item) => sum + (item.size || 0), 0);

      return {
        total: files.length,
        folders,
        files: regularFiles,
        totalSize,
      };
    },
  },

  mounted() {
    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeyboardShortcuts);
    // 点击其他地方关闭右键菜单
    document.addEventListener('click', this.hideContextMenu);

    // 添加拖拽事件监听
    document.addEventListener('dragover', this.handleGlobalDragOver);
    document.addEventListener('drop', this.handleGlobalDrop);
    document.addEventListener('dragenter', this.handleGlobalDragEnter);
    document.addEventListener('dragleave', this.handleGlobalDragLeave);
  },

  beforeDestroy() {
    // 清理事件监听器
    document.removeEventListener('keydown', this.handleKeyboardShortcuts);
    document.removeEventListener('click', this.hideContextMenu);
    document.removeEventListener('dragover', this.handleGlobalDragOver);
    document.removeEventListener('drop', this.handleGlobalDrop);
    document.removeEventListener('dragenter', this.handleGlobalDragEnter);
    document.removeEventListener('dragleave', this.handleGlobalDragLeave);
  },

  methods: {
    // 切换视图模式
    toggleViewMode() {
      this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid';
    },

    // 获取文件图标
    getFileIcon(item) {
      if (!item) return 'el-icon-document'; // 添加空值检查，返回默认图标

      const iconMap = {
        folder: 'el-icon-folder',
        image: 'el-icon-picture-outline',
        document: 'el-icon-document',
        pdf: 'el-icon-document',
        excel: 'el-icon-s-grid',
        text: 'el-icon-document-copy',
        video: 'el-icon-video-camera-solid',
        audio: 'el-icon-headset',
        code: 'el-icon-document',
        json: 'el-icon-document',
        design: 'el-icon-brush',
      };
      return iconMap[item.type] || 'el-icon-document';
    },

    // 获取文件颜色
    getFileColor(item) {
      if (!item) return '#909399'; // 添加空值检查，返回默认颜色

      const colorMap = {
        folder: '#409EFF',
        image: '#67C23A',
        document: '#E6A23C',
        pdf: '#F56C6C',
        excel: '#67C23A',
        text: '#909399',
        video: '#409EFF',
        audio: '#E6A23C',
        code: '#F56C6C',
        json: '#409EFF',
        design: '#E6A23C',
      };
      return colorMap[item.type] || '#909399';
    },

    // 获取文件扩展名
    getFileExtension(fileName) {
      const ext = fileName.split('.').pop();
      return ext ? ext.toUpperCase() : '';
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size || size === 0) return '-';
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      let fileSize = size;

      while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024;
        index++;
      }

      return `${fileSize.toFixed(index > 0 ? 1 : 0)} ${units[index]}`;
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    },

    // 处理搜索
    handleSearch() {
      // 搜索功能实现
      if (!this.searchQuery.trim()) {
        return;
      }

      const query = this.searchQuery.toLowerCase();
      this.searchResults = this.findFilesRecursively(this.fileSystem, query);
    },
    // 递归搜索文件
    findFilesRecursively(items, query) {
      const results = [];

      items.forEach((item) => {
        if (item.name.toLowerCase().includes(query)) {
          results.push({
            ...item,
            path: this.getCurrentPath() + '/' + item.name,
          });
        }

        if (item.children) {
          const childResults = this.findFilesRecursively(item.children, query);
          results.push(...childResults);
        }
      });

      return results;
    },
    // 处理键盘快捷键
    handleKeyboardShortcuts(event) {
      // Ctrl+A: 全选
      if (event.ctrlKey && event.key === 'a') {
        event.preventDefault();
        this.selectAll();
      }

      // Delete: 删除选中文件
      if (event.key === 'Delete' && this.selectedItem) {
        this.deleteFile(this.selectedItem);
      }

      // F2: 重命名
      if (event.key === 'F2' && this.selectedItem) {
        this.renameFile(this.selectedItem);
      }

      // Escape: 取消选择
      if (event.key === 'Escape') {
        this.selectedItem = null;
        this.hideFI();
      }

      // Ctrl+V: 粘贴
      if (event.ctrlKey && event.key === 'v') {
        this.pasteFiles();
      }
    },
    // 全选功能
    selectAll() {
      this.batchSelectMode = true;
      this.selectedItems = [...this.currentFiles];
    },

    // 粘贴文件
    pasteFiles() {
      if (this.clipboardItems.length > 0) {
        this.clipboardItems.forEach((item) => {
          this.copyFile(item);
        });
        this.clipboardItems = [];
        this.$message.success(`粘贴了 ${this.clipboardItems.length} 个项目`);
      }
    },

    // 批量删除
    batchDelete() {
      const count = this.selectedItems.length;
      this.$confirm(`确定要删除选中的 ${count} 个项目吗？`, '批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'glass-message-box',
      }).then(() => {
        const currentDir = this.getCurrentDirectory();
        this.selectedItems.forEach((item) => {
          const index = currentDir.findIndex((file) => file.id === item.id);
          if (index > -1) {
            currentDir.splice(index, 1);
          }
        });

        this.selectedItems = [];
        this.batchSelectMode = false;
        this.$message.success(`成功删除 ${count} 个项目`);
      });
    },

    // 显示文件详情面板
    showFileDetails(item) {
      this.detailsPanelFile = item;
      this.showDetailsPanel = true;
    },

    // 添加文件标签
    addFileTag(tagName) {
      if (this.detailsPanelFile && tagName.trim()) {
        if (!this.detailsPanelFile.tags) {
          this.detailsPanelFile.tags = [];
        }

        if (!this.detailsPanelFile.tags.includes(tagName)) {
          this.detailsPanelFile.tags.push(tagName);
        }
      }
    },

    // 删除文件标签
    removeFileTag(tagName) {
      if (this.detailsPanelFile && this.detailsPanelFile.tags) {
        const index = this.detailsPanelFile.tags.indexOf(tagName);
        if (index > -1) {
          this.detailsPanelFile.tags.splice(index, 1);
        }
      }
    },

    // 双击处理
    handleItemDoubleClick(item) {
      if (item.type === 'folder') {
        this.enterFolder(item);
      } else {
        this.openFilePreview(item);
      }
    },

    // 进入文件夹
    enterFolder(folder) {
      this.loading = true;
      setTimeout(() => {
        this.currentPath.push(folder.name);
        this.selectedItem = null;
        this.loading = false;
      }, 300);
    },

    // 导航到指定路径
    navigateToPath(index) {
      if (index < this.currentPath.length - 1) {
        this.loading = true;
        setTimeout(() => {
          this.currentPath = this.currentPath.slice(0, index + 1);
          this.selectedItem = null;
          this.loading = false;
        }, 200);
      }
    },

    // 选择项目
    selectItem(item) {
      this.selectedItem = item;
    },

    // 获取表格行样式
    getRowClassName({ row }) {
      return this.selectedItem?.id === row.id ? 'selected-row' : '';
    },

    // 创建文件夹
    createFolder() {
      this.$refs.folderForm.validate((valid) => {
        if (valid) {
          const newFolder = {
            id: Date.now(),
            name: this.folderForm.name,
            type: 'folder',
            size: 0,
            modifiedAt: new Date(),
            children: [],
          };

          // 添加到当前目录
          this.getCurrentDirectory().push(newFolder);

          this.cancelCreateFolder();
          this.$message.success('文件夹创建成功');
        }
      });
    },

    // 取消创建文件夹
    cancelCreateFolder() {
      this.showCreateFolderDialog = false;
      this.folderForm.name = '';
      this.$refs.folderForm?.resetFields();
    },

    // 获取当前目录的引用
    getCurrentDirectory() {
      let files = this.fileSystem['/'];

      for (let i = 1; i < this.currentPath.length; i++) {
        const folderName = this.currentPath[i];
        const folder = files.find((item) => item.name === folderName && item.type === 'folder');
        if (folder && folder.children) {
          files = folder.children;
        }
      }

      return files;
    },

    // 导入文件
    handleImportFile() {
      this.$refs.fileInput.click();
    },

    // 处理文件上传
    handleFileUpload(event) {
      const files = Array.from(event.target.files);
      const currentDir = this.getCurrentDirectory();

      this.loading = true;

      setTimeout(() => {
        files.forEach((file) => {
          const fileItem = {
            id: Date.now() + Math.random(),
            name: file.name,
            type: this.getFileTypeFromName(file.name),
            size: file.size,
            modifiedAt: new Date(),
            file: file, // 保存原始文件对象用于预览
          };

          currentDir.push(fileItem);
        });

        this.loading = false;
        this.$message.success(`成功导入 ${files.length} 个文件`);
        event.target.value = ''; // 清空input
      }, 500);
    },

    // 根据文件名获取文件类型
    getFileTypeFromName(fileName) {
      const ext = fileName.split('.').pop().toLowerCase();
      const typeMap = {
        jpg: 'image',
        jpeg: 'image',
        png: 'image',
        gif: 'image',
        svg: 'image',
        pdf: 'pdf',
        doc: 'document',
        docx: 'document',
        xls: 'excel',
        xlsx: 'excel',
        txt: 'text',
        md: 'text',
        mp4: 'video',
        avi: 'video',
        mov: 'video',
        mp3: 'audio',
        wav: 'audio',
        js: 'code',
        html: 'code',
        css: 'code',
        vue: 'code',
        json: 'json',
        sketch: 'design',
        psd: 'design',
        ai: 'design',
      };
      return typeMap[ext] || 'document';
    },

    // 预览文件
    openFilePreview(item) {
      if (!item) return; // 如果 item 为空，直接返回

      this.previewFile = item;
      this.previewType = item.type;

      if (item.file) {
        // 新上传的文件
        this.previewUploadedFile(item);
      } else {
        // 示例文件
        this.previewSampleFile(item);
      }

      this.showPreviewDialog = true;
    },

    // 预览上传的文件
    previewUploadedFile(item) {
      if (item.type === 'image') {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.previewContent = e.target.result;
        };
        reader.readAsDataURL(item.file);
      } else if (item.type === 'text') {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.previewContent = e.target.result;
        };
        reader.readAsText(item.file);
      } else {
        this.previewType = 'unsupported';
      }
    },

    // 预览示例文件
    previewSampleFile(item) {
      if (item.type === 'image') {
        this.previewContent = 'https://picsum.photos/800/600?random=' + item.id;
      } else if (item.type === 'text') {
        this.previewContent = `这是 ${item.name} 的示例内容。

在实际应用中，这里会显示文件的真实内容。

文件信息:
- 文件名: ${item.name}
- 文件大小: ${this.formatFileSize(item.size)}
- 修改时间: ${this.formatDate(item.modifiedAt)}
- 文件类型: ${item.type}

这是一段示例文本，用于演示文本文件预览功能。
在真实环境中，这里会显示文件的实际内容。`;
      } else if (item.type === 'pdf') {
        this.previewContent =
          'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf';
      } else {
        this.previewType = 'unsupported';
      }
    },

    // 删除文件
    deleteFile(item) {
      this.$confirm(`确定要删除 "${item.name}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'glass-message-box',
      })
        .then(() => {
          const currentDir = this.getCurrentDirectory();
          const index = currentDir.findIndex((file) => file.id === item.id);
          if (index > -1) {
            currentDir.splice(index, 1);
            this.$message.success('删除成功');
            if (this.selectedItem?.id === item.id) {
              this.selectedItem = null;
            }
          }
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 显示右键菜单
    showContextMenu(event, item) {
      event.preventDefault();
      this.contextMenuItem = item;
      this.contextMenuStyle = {
        left: event.pageX + 'px',
        top: event.pageY + 'px',
      };
      this.showContextMenuFlag = true;
    },

    // 隐藏右键菜单
    hideContextMenu() {
      this.showContextMenuFlag = false;
    },

    // 处理菜单操作
    handleMenuAction(action) {
      this.hideContextMenu();

      switch (action) {
        case 'preview':
          this.openFilePreview(this.contextMenuItem);
          break;
        case 'rename':
          this.renameFile(this.contextMenuItem);
          break;
        case 'copy':
          this.copyFile(this.contextMenuItem);
          break;
        case 'delete':
          this.deleteFile(this.contextMenuItem);
          break;
      }
    },

    // 重命名文件
    renameFile(item) {
      this.$prompt('请输入新名称', '重命名', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: item.name,
        inputPattern: /^[^\\/:*?"<>|]+$/,
        inputErrorMessage: '文件名不能包含特殊字符',
        customClass: 'glass-message-box',
      })
        .then(({ value }) => {
          item.name = value;
          this.$message.success('重命名成功');
        })
        .catch(() => {
          this.$message.info('已取消重命名');
        });
    },

    // 复制文件
    copyFile(item) {
      const currentDir = this.getCurrentDirectory();
      const copyItem = {
        ...item,
        id: Date.now() + Math.random(),
        name: item.name.includes('.')
          ? item.name.replace(/(\.[^.]+)$/, ' - 副本$1')
          : item.name + ' - 副本',
        modifiedAt: new Date(),
      };

      if (item.children) {
        copyItem.children = [...item.children];
      }

      currentDir.push(copyItem);
      this.$message.success('文件已复制');
    },

    // 处理预览对话框关闭
    handlePreviewDialogClosed() {
      // 清理预览相关数据，避免下次打开时出现问题
      this.previewContent = '';
      // 不要清空 previewFile，因为可能在其他地方还需要使用
    },
  },
};
</script>

<style lang="scss" scoped>
.file-manager {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
}

// 背景装饰
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;

  .floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;

    .shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      filter: blur(1px);
      animation: float 6s ease-in-out infinite;

      &.shape-1 {
        width: 80px;
        height: 80px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.shape-2 {
        width: 60px;
        height: 60px;
        top: 20%;
        right: 15%;
        animation-delay: -2s;
      }

      &.shape-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 5%;
        animation-delay: -4s;
      }

      &.shape-4 {
        width: 50px;
        height: 50px;
        bottom: 30%;
        right: 10%;
        animation-delay: -1s;
      }

      &.shape-5 {
        width: 120px;
        height: 120px;
        top: 50%;
        right: 5%;
        animation-delay: -3s;
      }
    }
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

// 主内容区域
.header,
.stats-bar,
.file-list-container {
  position: relative;
  z-index: 1;
}

// 头部样式
.header {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 30px;
    flex: 1;
    min-width: 300px;

    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 20px;
      font-weight: 600;
      color: white;

      i {
        font-size: 28px;
        color: #ffd700;
      }
    }
  }

  .nav-breadcrumb {
    flex: 1;

    ::v-deep .el-breadcrumb {
      .el-breadcrumb__item {
        .el-breadcrumb__inner {
          color: rgba(255, 255, 255, 0.9);
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 5px;

          i {
            font-size: 16px;
          }
        }

        &.clickable .el-breadcrumb__inner {
          cursor: pointer;

          &:hover {
            color: white;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
          }
        }
      }

      .el-breadcrumb__separator {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  .toolbar {
    display: flex;
    align-items: center;
    gap: 12px;

    .search-box {
      margin-right: 12px;

      .search-input {
        width: 200px;

        ::v-deep .el-input__inner {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;
          border-radius: 20px;

          &::placeholder {
            color: rgba(255, 255, 255, 0.6);
          }

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }

        ::v-deep .el-input__prefix {
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }
  }
}

// 玻璃按钮样式
.glass-button {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border-radius: 8px !important;
  font-weight: 500;

  &:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  &.primary {
    background: rgba(64, 158, 255, 0.3) !important;
    border-color: rgba(64, 158, 255, 0.5) !important;

    &:hover {
      background: rgba(64, 158, 255, 0.5) !important;
    }
  }

  &.success {
    background: rgba(103, 194, 58, 0.3) !important;
    border-color: rgba(103, 194, 58, 0.5) !important;

    &:hover {
      background: rgba(103, 194, 58, 0.5) !important;
    }
  }

  &.toggle {
    min-width: 80px;
  }
}

// 统计信息栏
.stats-bar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 15px 30px;
  display: flex;
  gap: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;

    i {
      font-size: 16px;
      color: #ffd700;
    }
  }
}

// 文件列表容器
.file-list-container {
  flex: 1;
  padding: 30px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;

  // 加载动画
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;

    .loading-spinner {
      text-align: center;
      color: white;

      .spinner-ring {
        display: inline-block;
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #409eff;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 15px;
      }

      .loading-text {
        font-size: 16px;
      }
    }
  }

  // 空状态
  .empty-state {
    text-align: center;
    padding: 80px 20px;
    color: rgba(255, 255, 255, 0.8);

    .empty-icon {
      font-size: 80px;
      margin-bottom: 20px;
      color: rgba(255, 255, 255, 0.5);
    }

    .empty-text {
      font-size: 18px;
      margin-bottom: 30px;
    }

    .empty-actions {
      display: flex;
      justify-content: center;
      gap: 15px;
      flex-wrap: wrap;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 网格视图
.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;

  .file-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.4);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);

      .file-actions {
        opacity: 1;
        transform: translateY(0);
      }
    }

    &.selected {
      background: rgba(64, 158, 255, 0.2);
      border-color: rgba(64, 158, 255, 0.5);
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
    }

    &.is-folder {
      &:hover {
        background: rgba(255, 215, 0, 0.1);
        border-color: rgba(255, 215, 0, 0.4);
      }
    }

    .file-icon-container {
      position: relative;
      margin-bottom: 15px;
    }

    .file-icon {
      font-size: 48px;
      margin-bottom: 15px;
      position: relative;
      display: inline-block;

      i {
        transition: all 0.3s ease;
      }

      .file-type-badge {
        position: absolute;
        bottom: -5px;
        right: -10px;
        background: rgba(255, 255, 255, 0.9);
        color: #333;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 8px;
        font-weight: 600;
      }
    }

    &:hover .file-icon i {
      transform: scale(1.1);
    }

    .file-name {
      color: white;
      font-weight: 500;
      margin-bottom: 8px;
      word-break: break-word;
      font-size: 14px;
      line-height: 1.4;
    }

    .file-info {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }

    .file-actions {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 5px;
      opacity: 0;
      transform: translateY(-10px);
      transition: all 0.3s ease;

      .action-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        padding: 6px;
        border-radius: 6px;
        font-size: 14px;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }

        &.danger:hover {
          background: rgba(245, 108, 108, 0.3);
        }
      }
    }
  }
}

// 列表视图
.list-view {
  .glass-table {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);

    ::v-deep {
      .el-table__header-wrapper {
        background: rgba(255, 255, 255, 0.1);

        th {
          background: transparent;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.9);
          font-weight: 600;
        }
      }

      .el-table__body-wrapper {
        tr {
          background: transparent;

          &:hover {
            background: rgba(255, 255, 255, 0.1) !important;
          }

          &.selected-row {
            background: rgba(64, 158, 255, 0.2) !important;
          }

          td {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
          }
        }
      }

      .el-table__empty-block {
        background: rgba(255, 255, 255, 0.05);
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .table-icon {
      font-size: 20px;
      text-align: center;
    }

    .file-name-cell {
      display: flex;
      align-items: center;
      gap: 10px;

      .file-type-tag {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-size: 10px;
      }
    }

    .file-size-cell,
    .file-date-cell {
      font-family: 'JetBrains Mono', monospace;
    }

    .table-actions {
      display: flex;
      gap: 5px;

      .action-button {
        color: rgba(255, 255, 255, 0.8);
        padding: 5px;

        &:hover {
          color: white;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        &.danger:hover {
          color: #f56c6c;
        }
      }
    }
  }
}

// 对话框样式
::v-deep .glass-dialog {
  .el-dialog {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);

    .el-dialog__header {
      background: rgba(255, 255, 255, 0.1);
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 16px 16px 0 0;

      .el-dialog__title {
        color: white;
        font-weight: 600;
      }

      .el-dialog__headerbtn {
        .el-dialog__close {
          color: rgba(255, 255, 255, 0.8);

          &:hover {
            color: white;
          }
        }
      }
    }

    .el-dialog__body {
      color: white;

      .dialog-content {
        .dialog-icon {
          text-align: center;
          margin-bottom: 20px;

          i {
            font-size: 48px;
            color: #67c23a;
          }
        }
      }

      .el-form-item__label {
        color: rgba(255, 255, 255, 0.9);
      }

      .el-input__inner {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 8px;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }

      .el-input__count {
        color: rgba(255, 255, 255, 0.6);
        background: transparent;
      }
    }

    .el-dialog__footer {
      border-top: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 0 0 16px 16px;
    }
  }
}

// 预览对话框特殊样式
::v-deep .preview-dialog .el-dialog {
  max-width: 90vw;

  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
  }
}

// 预览内容
.preview-content {
  .preview-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 20px;
    margin-bottom: 20px;

    .file-info-detail {
      display: flex;
      align-items: center;
      gap: 20px;

      .file-icon-large {
        font-size: 48px;
      }

      .file-details {
        h3 {
          margin: 0 0 10px 0;
          color: white;
          font-size: 20px;
        }

        p {
          margin: 5px 0;
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;
        }
      }
    }
  }

  .image-preview {
    text-align: center;

    .image-container {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 12px;
      padding: 20px;
      display: inline-block;

      img {
        max-width: 100%;
        max-height: 500px;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .text-preview {
    .text-container {
      background: rgba(0, 0, 0, 0.4);
      border-radius: 12px;
      padding: 25px;
      max-height: 500px;
      overflow-y: auto;
      border: 1px solid rgba(255, 255, 255, 0.1);

      pre {
        color: rgba(255, 255, 255, 0.95);
        font-family: 'JetBrains Mono', 'Courier New', monospace;
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        margin: 0;
      }
    }
  }

  .pdf-preview {
    iframe {
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
  }

  .unsupported-preview {
    text-align: center;
    padding: 60px 20px;

    .unsupported-icon {
      font-size: 80px;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 20px;
    }

    h3 {
      color: white;
      margin-bottom: 15px;
      font-size: 24px;
    }

    p {
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 30px;
      font-size: 16px;
    }
  }
}

// 右键菜单
.context-menu {
  position: fixed;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 8px 0;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  min-width: 150px;

  .menu-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }

    &.danger {
      &:hover {
        background: rgba(245, 108, 108, 0.2);
        color: #f56c6c;
      }
    }

    i {
      font-size: 16px;
      width: 16px;
      text-align: center;
    }
  }
}

// 消息框样式
::v-deep .glass-message-box {
  .el-message-box {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;

    .el-message-box__header {
      .el-message-box__title {
        color: white;
      }
    }

    .el-message-box__content {
      color: rgba(255, 255, 255, 0.9);

      .el-message-box__input input {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }
      }
    }

    .el-message-box__btns {
      .el-button {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        &.el-button--primary {
          background: rgba(64, 158, 255, 0.3);
          border-color: rgba(64, 158, 255, 0.5);

          &:hover {
            background: rgba(64, 158, 255, 0.5);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .grid-view {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .file-manager {
    .header {
      flex-direction: column;
      padding: 15px 20px;

      .header-left {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

        .logo {
          align-self: center;
        }
      }

      .toolbar {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;

        .search-box .search-input {
          width: 100%;
          min-width: 200px;
        }
      }
    }

    .stats-bar {
      flex-wrap: wrap;
      gap: 15px;
      padding: 15px 20px;
    }

    .file-list-container {
      padding: 20px 15px;
    }

    .grid-view {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 12px;

      .file-item {
        padding: 15px;

        .file-icon {
          font-size: 36px;
          margin-bottom: 12px;
        }

        .file-name {
          font-size: 13px;
        }

        .file-info {
          font-size: 11px;
          flex-direction: column;
          gap: 2px;
        }
      }
    }
  }

  ::v-deep .glass-dialog .el-dialog {
    width: 95% !important;
    margin: 20px auto !important;
  }
}

@media (max-width: 480px) {
  .grid-view {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .context-menu {
    min-width: 120px;

    .menu-item {
      padding: 8px 12px;
      font-size: 13px;
    }
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

// 选中状态动画
@keyframes selectPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

.file-item.selected {
  animation: selectPulse 1.5s infinite;
}

// 文件图标颜色
.file-icon {
  &.folder {
    color: #ffd700;
  }
  &.image {
    color: #ff6b6b;
  }
  &.document {
    color: #4ecdc4;
  }
  &.pdf {
    color: #ff4757;
  }
  &.excel {
    color: #2ed573;
  }
  &.text {
    color: #ffa502;
  }
  &.video {
    color: #3742fa;
  }
  &.audio {
    color: #ff6348;
  }
  &.code {
    color: #2f3542;
  }
  &.design {
    color: #ff3838;
  }
  &.json {
    color: #ffc048;
  }
}

// 拖拽样式
.drag-over {
  background: rgba(64, 158, 255, 0.2) !important;
  border: 2px dashed rgba(64, 158, 255, 0.8) !important;
}

.drag-item {
  opacity: 0.6;
  transform: rotate(5deg);
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

// 文件类型标签颜色
.file-type-badge {
  &.folder {
    background: linear-gradient(45deg, #ffd700, #ffa500);
  }
  &.image {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  }
  &.document {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
  }
  &.pdf {
    background: linear-gradient(45deg, #ff4757, #ff3742);
  }
  &.excel {
    background: linear-gradient(45deg, #2ed573, #7bed9f);
  }
  &.text {
    background: linear-gradient(45deg, #ffa502, #ff9f43);
  }
  &.video {
    background: linear-gradient(45deg, #3742fa, #5352ed);
  }
  &.audio {
    background: linear-gradient(45deg, #ff6348, #ff7675);
  }
  &.code {
    background: linear-gradient(45deg, #57606f, #2f3542);
  }
  &.design {
    background: linear-gradient(45deg, #ff3838, #ff4757);
  }
  &.json {
    background: linear-gradient(45deg, #ffc048, #feca57);
  }
}

// 加载动画增强
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 工具提示样式
::v-deep .el-tooltip__popper {
  background: rgba(0, 0, 0, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 6px;

  &[x-placement^='top'] .popper__arrow::after {
    border-top-color: rgba(0, 0, 0, 0.8);
  }
}

// 消息通知样式
::v-deep .el-message {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

  .el-message__icon {
    color: white;
  }
}

// 进度条样式
::v-deep .el-progress-bar__outer {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

::v-deep .el-progress-bar__inner {
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 10px;
}

// 表格行选中效果
.table-row-selected {
  background: rgba(64, 158, 255, 0.15) !important;

  td {
    border-color: rgba(64, 158, 255, 0.3) !important;
  }
}

// 文件夹展开动画
.folder-expand-enter-active,
.folder-expand-leave-active {
  transition: all 0.3s ease;
}

.folder-expand-enter,
.folder-expand-leave-to {
  transform: scale(0.8);
  opacity: 0;
}

// 搜索结果高亮
.search-highlight {
  background: rgba(255, 255, 0, 0.3);
  padding: 1px 2px;
  border-radius: 2px;
}

// 上下文菜单分割线
.context-menu-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
  margin: 5px 10px;
}

// 文件上传拖拽区域
.upload-drop-zone {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(64, 158, 255, 0.1);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .drop-zone-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 2px dashed rgba(64, 158, 255, 0.8);
    border-radius: 20px;
    padding: 60px;
    text-align: center;
    color: white;

    .drop-icon {
      font-size: 80px;
      color: #409eff;
      margin-bottom: 20px;
    }

    .drop-text {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .drop-hint {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.7);
    }
  }
}

// 批量选择模式
.batch-select-mode {
  .file-item {
    .selection-checkbox {
      position: absolute;
      top: 10px;
      left: 10px;
      opacity: 1;
      transform: scale(1);
      transition: all 0.3s ease;
    }
  }
}

.selection-checkbox {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;

  ::v-deep .el-checkbox__inner {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);

    &:hover {
      border-color: #409eff;
    }
  }

  ::v-deep .el-checkbox__input.is-checked {
    .el-checkbox__inner {
      background: #409eff;
      border-color: #409eff;
    }
  }
}

// 批量操作栏
.batch-actions-bar {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 15px 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 1000;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

  .selected-count {
    color: white;
    font-weight: 600;
  }

  .action-buttons {
    display: flex;
    gap: 10px;

    .batch-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      &.danger {
        background: rgba(245, 108, 108, 0.2);
        border-color: rgba(245, 108, 108, 0.5);

        &:hover {
          background: rgba(245, 108, 108, 0.3);
        }
      }
    }
  }
}

// 文件详情面板
.file-details-panel {
  position: fixed;
  right: -400px;
  top: 0;
  width: 400px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(25px);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  transition: right 0.3s ease;
  overflow-y: auto;

  &.show {
    right: 0;
  }

  .panel-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .panel-title {
      color: white;
      font-weight: 600;
      font-size: 18px;
    }

    .close-btn {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.7);
      font-size: 20px;
      cursor: pointer;

      &:hover {
        color: white;
      }
    }
  }

  .panel-content {
    padding: 20px;
    color: white;

    .file-preview-mini {
      text-align: center;
      margin-bottom: 20px;

      .preview-icon {
        font-size: 60px;
        margin-bottom: 15px;
        display: block;
      }

      .preview-image {
        max-width: 100%;
        border-radius: 8px;
      }
    }

    .detail-item {
      margin-bottom: 15px;

      .detail-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 5px;
      }

      .detail-value {
        font-size: 16px;
        color: white;
        font-weight: 500;
      }
    }

    .tags-section {
      .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 10px;

        .tag {
          background: rgba(64, 158, 255, 0.3);
          border: 1px solid rgba(64, 158, 255, 0.5);
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
        }

        .add-tag {
          background: rgba(255, 255, 255, 0.1);
          border: 1px dashed rgba(255, 255, 255, 0.5);
          color: rgba(255, 255, 255, 0.7);
          cursor: pointer;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
          }
        }
      }
    }
  }
}

// 快捷键提示
.shortcuts-help {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 30px;
  z-index: 9999;
  max-width: 500px;

  .shortcuts-title {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
  }

  .shortcut-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    color: rgba(255, 255, 255, 0.9);

    .shortcut-keys {
      font-family: 'JetBrains Mono', monospace;
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
  }
}

// 性能优化：虚拟滚动容器
.virtual-scroll-container {
  height: 100%;
  overflow: hidden;
}

// 文件缩略图加载
.thumbnail-loading {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: loading-shimmer 2s infinite;
  }
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// 文件传输进度
.transfer-progress {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
  min-width: 300px;
  z-index: 1000;

  .progress-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    color: white;

    .file-icon {
      font-size: 16px;
    }

    .file-info {
      flex: 1;

      .file-name {
        font-size: 14px;
        margin-bottom: 2px;
      }

      .progress-text {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
      }
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #409eff, #67c23a);
        border-radius: 2px;
        transition: width 0.3s ease;
      }
    }
  }
}
</style>
