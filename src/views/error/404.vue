<template>
  <div class="not-found">
    <div class="container">
      <h1 class="error-code">404</h1>
      <p class="error-message">抱歉，您访问的页面不存在</p>
      <router-link to="/" class="home-link"> 返回首页 </router-link>
    </div>
  </div>
</template>

<script>
export default {
  name: "NotFound",
};
</script>

<style scoped>
.not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f0f0f0;
  font-family: Arial, sans-serif;
}

.container {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 20px;
}

.error-code {
  font-size: 8rem;
  color: #ff4757;
  margin-bottom: 1rem;
  font-weight: bold;
}

.error-message {
  font-size: 1.5rem;
  color: #666;
  margin-bottom: 2rem;
}

.home-link {
  display: inline-block;
  padding: 1rem 2rem;
  background: #42b983;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-size: 1.1rem;
  transition: background 0.3s;
}

.home-link:hover {
  background: #3aa878;
}

/* 添加一些动画效果 */
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-code {
  animation: rotate 2s linear infinite;
}
</style>
