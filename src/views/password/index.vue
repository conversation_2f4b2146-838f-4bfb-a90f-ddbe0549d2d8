<template>
  <div class="password-manager">
    <!-- 3D立体背景 -->
    <div class="background-3d">
      <!-- 动态流星雨 -->
      <div class="meteor-shower">
        <div v-for="n in 15" :key="n" class="meteor" :style="getMeteorStyle()"></div>
      </div>
      
      <!-- 几何图形动画 -->
      <div class="geometric-shapes">
        <div class="shape-orbit">
          <div class="orbit-ring ring-1"></div>
          <div class="orbit-ring ring-2"></div>
          <div class="orbit-ring ring-3"></div>
          <div class="central-core"></div>
        </div>
        
        <div class="floating-cubes">
          <div class="cube cube-1"></div>
          <div class="cube cube-2"></div>
          <div class="cube cube-3"></div>
        </div>
      </div>
      
      <!-- 光波扩散效果 -->
      <div class="light-waves">
        <div class="wave wave-1"></div>
        <div class="wave wave-2"></div>
        <div class="wave wave-3"></div>
      </div>
      
      <!-- 数字雨特效 -->
      <div ref="matrixRain" class="matrix-rain">
        <canvas ref="matrixCanvas"></canvas>
      </div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
      <!-- 超级导航栏 -->
      <nav class="super-nav glass-premium">
        <div class="nav-left">
          <div class="brand-container">
            <div class="brand-logo">
              <div class="logo-rings">
                <div class="ring ring-outer"></div>
                <div class="ring ring-middle"></div>
                <div class="ring ring-inner"></div>
              </div>
              <div class="logo-center">
                <i class="el-icon-key"></i>
              </div>
            </div>
            <div class="brand-info">
              <h2 class="brand-name">CyberVault</h2>
              <p class="brand-tagline">数字安全守护</p>
            </div>
          </div>
          
          <!-- 导航菜单 -->
          <div class="nav-menu">
            <div class="menu-item" :class="{ active: currentTab === 'passwords' }" @click="currentTab = 'passwords'">
              <i class="el-icon-lock"></i>
              <span>密码库</span>
            </div>
            <div class="menu-item" :class="{ active: currentTab === 'generator' }" @click="currentTab = 'generator'">
              <i class="el-icon-cpu"></i>
              <span>生成器</span>
            </div>
            <div class="menu-item" :class="{ active: currentTab === 'security' }" @click="currentTab = 'security'">
              <i class="el-icon-warning-outline"></i>
              <span>安全检查</span>
            </div>
          </div>
        </div>
        
        <div class="nav-right">
          <!-- 实时统计 -->
          <div class="stats-dashboard">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-collection"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ passwords.length }}</div>
                <div class="stat-label">密码总数</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon security">
                <i class="el-icon-shield"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ getSecurityScore() }}%</div>
                <div class="stat-label">安全得分</div>
              </div>
            </div>
          </div>
          
          <!-- 用户菜单 -->
          <el-dropdown class="user-menu">
            <div class="user-avatar">
              <img src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" alt="用户头像">
              <div class="status-dot online"></div>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item><i class="el-icon-user"></i> 个人设置</el-dropdown-item>
              <el-dropdown-item><i class="el-icon-download"></i> 导出数据</el-dropdown-item>
              <el-dropdown-item><i class="el-icon-upload2"></i> 导入数据</el-dropdown-item>
              <el-dropdown-item divided><i class="el-icon-switch-button"></i> 退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </nav>

      <!-- 智能搜索栏 -->
      <div class="smart-search-section">
        <div class="search-container glass-premium">
          <div class="search-box">
            <div class="search-icon">
              <i class="el-icon-search"></i>
            </div>
            <input 
              v-model="searchKeyword" 
              type="text"
              class="search-input"
              placeholder="智能搜索密码、网站、用户名..."
              @focus="showSearchSuggestions = true"
              @blur="hideSearchSuggestions"
            >
            <div class="search-actions">
              <button class="voice-search" @click="startVoiceSearch">
                <i class="el-icon-microphone"></i>
              </button>
              <button class="ai-search" @click="showAISearch">
                <i class="el-icon-magic-stick"></i>
              </button>
            </div>
          </div>
          
          <!-- 搜索建议 -->
          <transition name="slide-fade">
            <div v-if="showSearchSuggestions && searchSuggestions.length" class="search-suggestions">
              <div class="suggestion-header">
                <span>搜索建议</span>
                <i class="el-icon-close" @click="showSearchSuggestions = false"></i>
              </div>
              <div class="suggestion-list">
                <div 
                  v-for="suggestion in searchSuggestions" 
                  :key="suggestion.id"
                  class="suggestion-item"
                  @click="selectSuggestion(suggestion)"
                >
                  <div class="suggestion-icon">
                    <i :class="getCategoryIcon(suggestion.category)"></i>
                  </div>
                  <div class="suggestion-content">
                    <div class="suggestion-name">{{ suggestion.siteName }}</div>
                    <div class="suggestion-username">{{ suggestion.username }}</div>
                  </div>
                  <div class="suggestion-category">{{ getCategoryName(suggestion.category) }}</div>
                </div>
              </div>
            </div>
          </transition>
        </div>
        
        <!-- 高级筛选 -->
        <div class="advanced-filters">
          <div class="filter-tabs">
            <div 
              v-for="category in categoryOptions" 
              :key="category.value"
              class="filter-tab"
              :class="{ active: selectedCategory === category.value }"
              @click="selectedCategory = category.value"
            >
              <div class="tab-icon">
                <i :class="category.icon"></i>
              </div>
              <span class="tab-label">{{ category.label }}</span>
              <div class="tab-count">{{ getFilterCount(category.value) }}</div>
            </div>
          </div>
          
          <!-- 排序选项 -->
          <div class="sort-options">
            <el-dropdown @command="handleSortChange">
              <span class="sort-trigger">
                <i class="el-icon-sort"></i>
                {{ getSortLabel(currentSort) }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="name">按名称排序</el-dropdown-item>
                <el-dropdown-item command="date">按创建时间</el-dropdown-item>
                <el-dropdown-item command="security">按安全等级</el-dropdown-item>
                <el-dropdown-item command="usage">按使用频率</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>

      <!-- 密码网格 - 高级卡片 -->
      <div class="password-grid-advanced">
        <transition-group name="card-list" class="grid-container">
          <div 
            v-for="(password, index) in filteredPasswords" 
            :key="password.id"
            class="advanced-password-card"
            :class="{ 
              selected: selectedPasswordId === password.id,
              favorite: password.isFavorite 
            }"
            :style="{ '--delay': index * 0.1 + 's' }"
            @click="selectPassword(password.id)"
          >
            <!-- 卡片装饰 -->
            <div class="card-decoration">
              <div class="decoration-line line-1"></div>
              <div class="decoration-line line-2"></div>
              <div class="decoration-dot dot-1"></div>
              <div class="decoration-dot dot-2"></div>
            </div>
            
            <!-- 卡片头部 -->
            <div class="card-header-advanced">
              <div class="site-avatar">
                <div class="avatar-bg" :style="{ background: getAvatarGradient(password.category) }">
                  <i :class="getCategoryIcon(password.category)"></i>
                </div>
                <div class="connection-indicator" :class="getConnectionStatus(password)"></div>
              </div>
              
              <div class="site-details">
                <h3 class="site-name-advanced">{{ password.siteName }}</h3>
                <p class="site-category">{{ getCategoryName(password.category) }}</p>
                <div v-if="password.website" class="site-url" @click="openWebsite(password.website)">
                  <i class="el-icon-link"></i>
                  {{ formatUrl(password.website) }}
                </div>
              </div>
              
              <div class="card-actions-header">
                <button 
                  class="favorite-btn-advanced" 
                  :class="{ active: password.isFavorite }"
                  @click.stop="toggleFavorite(password.id)"
                >
                  <i class="el-icon-star-on"></i>
                </button>
                
                <el-dropdown @command="(command) => handleCardAction(command, password)">
                  <button class="more-btn">
                    <i class="el-icon-more"></i>
                  </button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="edit">
                      <i class="el-icon-edit"></i> 编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="duplicate">
                      <i class="el-icon-copy-document"></i> 复制
                    </el-dropdown-item>
                    <el-dropdown-item command="export">
                      <i class="el-icon-download"></i> 导出
                    </el-dropdown-item>
                    <el-dropdown-item divided command="delete">
                      <i class="el-icon-delete" style="color: #f56c6c;"></i> 
                      <span style="color: #f56c6c;">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            
            <!-- 用户信息 -->
            <div class="user-info-advanced">
              <div class="info-item">
                <div class="info-icon">
                  <i class="el-icon-user"></i>
                </div>
                <div class="info-content">
                  <div class="info-label">用户名</div>
                  <div class="info-value">{{ password.username }}</div>
                </div>
                <button class="copy-btn-mini" @click.stop="copyToClipboard(password.username, '用户名')">
                  <i class="el-icon-copy-document"></i>
                </button>
              </div>
            </div>
            
            <!-- 密码信息 -->
            <div class="password-info-advanced">
              <div class="info-item">
                <div class="info-icon security-icon" :class="getSecurityLevel(password.password)">
                  <i class="el-icon-lock"></i>
                </div>
                <div class="info-content">
                  <div class="info-label">
                    密码
                    <span class="security-badge" :class="getSecurityLevel(password.password)">
                      {{ getSecurityText(password.password) }}
                    </span>
                  </div>
                  <div class="password-display">
                    <span v-if="!password.showPassword" class="password-masked">
                      {{ '•'.repeat(password.password.length) }}
                    </span>
                    <span v-else class="password-visible">{{ password.password }}</span>
                  </div>
                </div>
                <div class="password-actions-advanced">
                  <button 
                    class="action-btn-mini toggle-btn"
                    @click.stop="togglePasswordVisibility(password.id)"
                  >
                    <i :class="password.showPassword ? 'el-icon-view' : 'el-icon-hide'"></i>
                  </button>
                  <button 
                    class="action-btn-mini copy-btn"
                    @click.stop="copyPassword(password)"
                  >
                    <i class="el-icon-copy-document"></i>
                  </button>
                </div>
              </div>
              
              <!-- 密码强度条 -->
              <div class="strength-indicator">
                <div class="strength-bar">
                  <div 
                    class="strength-fill" 
                    :class="getSecurityLevel(password.password)"
                    :style="{ width: getPasswordStrength(password.password) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
            
            <!-- 额外信息 -->
            <div v-if="password.notes" class="extra-info">
              <div class="notes-section">
                <i class="el-icon-document"></i>
                <span>{{ password.notes }}</span>
              </div>
            </div>
            
            <!-- 卡片底部 -->
            <div class="card-footer-advanced">
              <div class="usage-stats">
                <div class="stat-item">
                  <i class="el-icon-time"></i>
                  <span>{{ formatDate(password.updatedAt) }}</span>
                </div>
                <div class="stat-item">
                  <i class="el-icon-view"></i>
                  <span>{{ password.usageCount || 0 }} 次使用</span>
                </div>
              </div>
              
              <div class="quick-actions">
                <button class="quick-btn" @click.stop="quickEdit(password)">
                  <i class="el-icon-edit-outline"></i>
                </button>
                <button v-if="password.website" class="quick-btn" @click.stop="openWebsite(password.website)">
                  <i class="el-icon-link"></i>
                </button>
                <button class="quick-btn share-btn" @click.stop="sharePassword(password)">
                  <i class="el-icon-share"></i>
                </button>
              </div>
            </div>
            
            <!-- 悬浮投影 -->
            <div class="card-glow"></div>
          </div>
        </transition-group>
        
        <!-- 高级空状态 -->
        <div v-if="filteredPasswords.length === 0" class="empty-state-advanced">
          <div class="empty-animation">
            <div class="empty-icon-container">
              <div class="floating-icons">
                <i class="el-icon-lock icon-float-1"></i>
                <i class="el-icon-key icon-float-2"></i>
                <i class="el-icon-shield icon-float-3"></i>
              </div>
            </div>
          </div>
          
          <div class="empty-content">
            <h3 class="empty-title">{{ getEmptyStateTitle() }}</h3>
            <p class="empty-description">{{ getEmptyStateDescription() }}</p>
            
            <div class="empty-actions">
              <el-button 
                type="primary" 
                size="large" 
                class="primary-action-btn"
                @click="showAddDialog = true"
              >
                <i class="el-icon-plus"></i>
                创建第一个密码
              </el-button>
              
              <el-button 
                size="large" 
                class="secondary-action-btn"
                @click="showImportDialog = true"
              >
                <i class="el-icon-upload2"></i>
                导入现有密码
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 超级浮动操作按钮 -->
    <div class="super-fab-container">
      <!-- AI 助手 -->
      <div class="fab-group">
        <button class="super-fab ai-fab" @click="openAIAssistant">
          <div class="fab-icon">
            <i class="el-icon-magic-stick"></i>
          </div>
          <div class="fab-ripple"></div>
        </button>
        <div class="fab-tooltip">AI 助手</div>
      </div>
      
      <!-- 快速添加 -->
      <div class="fab-group">
        <button class="super-fab add-fab" @click="quickAdd">
          <div class="fab-icon">
            <i class="el-icon-plus"></i>
          </div>
          <div class="fab-ripple"></div>
        </button>
        <div class="fab-tooltip">快速添加</div>
      </div>
      
      <!-- 密码生成器 -->
      <div class="fab-group">
        <button class="super-fab generator-fab" @click="openPasswordGenerator">
          <div class="fab-icon">
            <i class="el-icon-cpu"></i>
          </div>
          <div class="fab-ripple"></div>
        </button>
        <div class="fab-tooltip">密码生成器</div>
      </div>
    </div>

    <!-- 高级添加/编辑对话框 -->
    <el-dialog
      :title="editingItem ? '编辑密码' : '添加新密码'"
      :visible.sync="showAddDialog"
      :before-close="handleDialogClose"
      width="600px"
      custom-class="advanced-dialog"
      :close-on-click-modal="false"
    >
      <template #title>
        <div class="dialog-title-advanced">
          <div class="title-icon">
            <i :class="editingItem ? 'el-icon-edit' : 'el-icon-plus'"></i>
          </div>
          <div class="title-text">
            <h3>{{ editingItem ? '编辑密码信息' : '创建新密码' }}</h3>
            <p>{{ editingItem ? '修改您的密码信息' : '安全保存您的密码' }}</p>
          </div>
        </div>
      </template>

      <el-form 
        ref="passwordForm" 
        :model="passwordForm" 
        :rules="formRules" 
        label-width="100px"
        class="advanced-form"
      >
        <!-- 基本信息步骤 -->
        <div class="form-step active">
          <div class="step-header">
            <h4>基本信息</h4>
            <div class="step-indicator">1 / 3</div>
          </div>
          
          <div class="form-grid">
            <el-form-item label="网站名称" prop="siteName" class="form-item-enhanced">
              <div class="input-with-icon">
                <i class="el-icon-globe input-icon"></i>
                <el-input
                  v-model="passwordForm.siteName"
                  placeholder="如：GitHub、Gmail 等"
                  size="large"
                  @input="handleSiteNameChange"
                />
                <div v-if="siteNameSuggestions.length" class="input-suggestions">
                  <div 
                    v-for="suggestion in siteNameSuggestions" 
                    :key="suggestion"
                    class="suggestion"
                    @click="selectSiteNameSuggestion(suggestion)"
                  >
                    {{ suggestion }}
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="网站地址" prop="website" class="form-item-enhanced">
              <div class="input-with-icon">
                <i class="el-icon-link input-icon"></i>
                <el-input
                  v-model="passwordForm.website"
                  placeholder="https://example.com"
                  size="large"
                />
                <button 
                  v-if="passwordForm.website" 
                  class="input-action-btn"
                  @click="testWebsite"
                >
                  <i class="el-icon-view"></i>
                </button>
              </div>
            </el-form-item>

            <el-form-item label="分类" prop="category" class="form-item-enhanced">
              <el-select 
                v-model="passwordForm.category" 
                placeholder="选择分类"
                size="large"
                class="category-select"
              >
                <el-option
                  v-for="category in categoryOptions.filter(c => c.value)"
                  :key="category.value"
                  :label="category.label"
                  :value="category.value"
                >
                  <div class="category-option">
                    <i :class="category.icon"></i>
                    <span>{{ category.label }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 账户信息 -->
        <div class="form-step">
          <div class="step-header">
            <h4>账户信息</h4>
            <div class="step-indicator">2 / 3</div>
          </div>
          
          <el-form-item label="用户名" prop="username" class="form-item-enhanced">
            <div class="input-with-icon">
              <i class="el-icon-user input-icon"></i>
              <el-input
                v-model="passwordForm.username"
                placeholder="用户名或邮箱"
                size="large"
              />
              <button class="input-action-btn" @click="generateUsername">
                <i class="el-icon-refresh"></i>
              </button>
            </div>
          </el-form-item>

          <el-form-item label="密码" prop="password" class="form-item-enhanced">
            <div class="password-input-advanced">
              <div class="input-with-icon">
                <i class="el-icon-lock input-icon"></i>
                <el-input
                  v-model="passwordForm.password"
                  :type="showFormPassword ? 'text' : 'password'"
                  placeholder="输入或生成安全密码"
                  size="large"
                  @input="handlePasswordChange"
                />
                <button 
                  class="input-action-btn"
                  @click="showFormPassword = !showFormPassword"
                >
                  <i :class="showFormPassword ? 'el-icon-hide' : 'el-icon-view'"></i>
                </button>
                <button class="input-action-btn generate-btn" @click="generateRandomPassword">
                  <i class="el-icon-cpu"></i>
                </button>
              </div>
              
              <!-- 实时密码强度分析 -->
              <div class="password-strength-advanced">
                <div class="strength-visual">
                  <div class="strength-meter">
                    <div 
                      class="meter-fill"
                      :class="getPasswordStrengthClass(passwordForm.password)"
                      :style="{ width: getPasswordStrength(passwordForm.password) + '%' }"
                    ></div>
                  </div>
                  <div class="strength-score">
                    <span class="score-text">{{ getPasswordStrengthText(passwordForm.password) }}</span>
                    <span class="score-number">{{ getPasswordStrength(passwordForm.password) }}/100</span>
                  </div>
                </div>
                
                <!-- 密码要求检查 -->
                <div class="password-requirements">
                  <div class="requirement-item" :class="{ met: passwordForm.password.length >= 8 }">
                    <i class="el-icon-check"></i>
                    <span>至少 8 个字符</span>
                  </div>
                  <div class="requirement-item" :class="{ met: /[A-Z]/.test(passwordForm.password) }">
                    <i class="el-icon-check"></i>
                    <span>包含大写字母</span>
                  </div>
                  <div class="requirement-item" :class="{ met: /[a-z]/.test(passwordForm.password) }">
                    <i class="el-icon-check"></i>
                    <span>包含小写字母</span>
                  </div>
                  <div class="requirement-item" :class="{ met: /\d/.test(passwordForm.password) }">
                    <i class="el-icon-check"></i>
                    <span>包含数字</span>
                  </div>
                  <div class="requirement-item" :class="{ met: /[!@#$%^&*(),.?\":{}|<>]/.test(passwordForm.password) }">
                    <i class="el-icon-check"></i>
                    <span>包含特殊字符</span>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>

        <!-- 额外设置 -->
        <div class="form-step">
          <div class="step-header">
            <h4>额外设置</h4>
            <div class="step-indicator">3 / 3</div>
          </div>
          
          <el-form-item label="备注信息" class="form-item-enhanced">
            <el-input
              v-model="passwordForm.notes"
              type="textarea"
              :rows="3"
              placeholder="添加备注信息（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <div class="form-options">
            <el-checkbox v-model="passwordForm.isFavorite" class="favorite-checkbox">
              <i class="el-icon-star-on"></i>
              标记为收藏
            </el-checkbox>
            
            <el-checkbox v-model="passwordForm.enableAutoFill" class="autofill-checkbox">
              <i class="el-icon-magic-stick"></i>
              启用自动填充
            </el-checkbox>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer-advanced">
          <div class="footer-left">
            <el-button class="reset-btn" @click="resetForm">
              <i class="el-icon-refresh-left"></i>
              重置
            </el-button>
          </div>
          
          <div class="footer-right">
            <el-button size="large" @click="handleDialogClose">
              取消
            </el-button>
            <el-button 
              type="primary" 
              :loading="saving" 
              size="large"
              class="save-btn"
              @click="savePassword"
            >
              <i v-if="!saving" :class="editingItem ? 'el-icon-check' : 'el-icon-plus'"></i>
              {{ saving ? '保存中...' : (editingItem ? '更新密码' : '保存密码') }}
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 密码生成器对话框 -->
    <el-dialog
      title="高级密码生成器"
      :visible.sync="showGeneratorDialog"
      width="500px"
      custom-class="generator-dialog"
    >
      <!-- 密码生成器内容 -->
      <div class="password-generator-advanced">
        <div class="generator-result">
          <div class="generated-password">
            <input 
              type="text" 
              :value="generatedPassword" 
              readonly 
              class="password-output"
            >
            <div class="result-actions">
              <button class="action-btn" @click="copyGeneratedPassword">
                <i class="el-icon-copy-document"></i>
              </button>
              <button class="action-btn" @click="regeneratePassword">
                <i class="el-icon-refresh"></i>
              </button>
            </div>
          </div>
          
          <div class="generation-options">
            <div class="option-group">
              <label>长度: {{ generatorOptions.length }}</label>
              <el-slider
                v-model="generatorOptions.length"
                :min="8"
                :max="50"
                @change="regeneratePassword"
              ></el-slider>
            </div>
            
            <div class="option-checkboxes">
              <el-checkbox v-model="generatorOptions.includeUppercase" @change="regeneratePassword">
                大写字母 (A-Z)
              </el-checkbox>
              <el-checkbox v-model="generatorOptions.includeLowercase" @change="regeneratePassword">
                小写字母 (a-z)
              </el-checkbox>
                            <el-checkbox v-model="generatorOptions.includeNumbers" @change="regeneratePassword">
                数字 (0-9)
              </el-checkbox>
              <el-checkbox v-model="generatorOptions.includeSymbols" @change="regeneratePassword">
                特殊字符 (!@#$...)
              </el-checkbox>
              <el-checkbox v-model="generatorOptions.excludeSimilar" @change="regeneratePassword">
                排除相似字符 (0oO1lI)
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AdvancedPasswordManager',
  data() {
    return {
      // 基础数据
      searchKeyword: '',
      selectedCategory: '',
      selectedPasswordId: null,
      showAddDialog: false,
      showGeneratorDialog: false,
      showFormPassword: false,
      editingItem: null,
      saving: false,
      currentTab: 'passwords',
      currentSort: 'name',
      
      // 搜索相关
      showSearchSuggestions: false,
      searchSuggestions: [],
      siteNameSuggestions: [],
      
      // 密码生成器
      generatedPassword: '',
      generatorOptions: {
        length: 16,
        includeUppercase: true,
        includeLowercase: true,
        includeNumbers: true,
        includeSymbols: true,
        excludeSimilar: false
      },
      
      // 示例密码数据
      passwords: [
        {
          id: 1,
          siteName: 'Gmail',
          username: '<EMAIL>',
          password: 'MySecurePass123!@#',
          website: 'https://gmail.com',
          category: 'email',
          showPassword: false,
          isFavorite: true,
          notes: '主要邮箱账号',
          usageCount: 45,
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-20'),
          enableAutoFill: true
        },
        {
          id: 2,
          siteName: 'GitHub',
          username: 'developer123',
          password: 'GitHubToken789&*()',
          website: 'https://github.com',
          category: 'work',
          showPassword: false,
          isFavorite: true,
          notes: '开发工作账号',
          usageCount: 32,
          createdAt: new Date('2024-01-12'),
          updatedAt: new Date('2024-01-22'),
          enableAutoFill: false
        },
        {
          id: 3,
          siteName: '微信',
          username: 'wechat_user',
          password: 'WeChatSecure456#$%',
          website: '',
          category: 'social',
          showPassword: false,
          isFavorite: false,
          notes: '',
          usageCount: 12,
          createdAt: new Date('2024-01-10'),
          updatedAt: new Date('2024-01-18'),
          enableAutoFill: true
        }
      ],
      
      // 表单数据
      passwordForm: {
        siteName: '',
        username: '',
        password: '',
        website: '',
        category: 'other',
        isFavorite: false,
        notes: '',
        enableAutoFill: false
      },
      
      // 表单验证规则
      formRules: {
        siteName: [
          { required: true, message: '请输入网站名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少6位', trigger: 'blur' }
        ]
      },
      
      // 分类选项
      categoryOptions: [
        { label: '全部', value: '', icon: 'el-icon-menu' },
        { label: '社交', value: 'social', icon: 'el-icon-chat-dot-round' },
        { label: '邮箱', value: 'email', icon: 'el-icon-message' },
        { label: '工作', value: 'work', icon: 'el-icon-suitcase' },
        { label: '购物', value: 'shopping', icon: 'el-icon-shopping-cart-2' },
        { label: '娱乐', value: 'entertainment', icon: 'el-icon-video-play' },
        { label: '金融', value: 'finance', icon: 'el-icon-coin' },
        { label: '其他', value: 'other', icon: 'el-icon-more' }
      ]
    }
  },

  computed: {
    // 过滤后的密码列表
    filteredPasswords() {
      let filtered = this.passwords

      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(p => 
          p.siteName.toLowerCase().includes(keyword) ||
          p.username.toLowerCase().includes(keyword) ||
          (p.website && p.website.toLowerCase().includes(keyword)) ||
          (p.notes && p.notes.toLowerCase().includes(keyword))
        )
      }

      // 分类过滤
      if (this.selectedCategory) {
        filtered = filtered.filter(p => p.category === this.selectedCategory)
      }

      // 排序
      return this.sortPasswords(filtered)
    }
  },

  watch: {
    searchKeyword: {
      handler(val) {
        if (val) {
          this.updateSearchSuggestions()
        } else {
          this.searchSuggestions = []
        }
      },
      immediate: true
    },
    
    'passwordForm.siteName': {
      handler(val) {
        if (val && val.length > 1) {
          this.updateSiteNameSuggestions(val)
        } else {
          this.siteNameSuggestions = []
        }
      }
    }
  },

  mounted() {
    this.initMatrixRain()
    this.generateRandomPassword()
    this.startAutoHidePasswords()
  },

  methods: {
    // 初始化数字雨特效
    initMatrixRain() {
      const canvas = this.$refs.matrixCanvas
      if (!canvas) return
      
      const ctx = canvas.getContext('2d')
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      
      const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン'
      const charArray = chars.split('')
      const drops = []
      
      for (let x = 0; x < canvas.width / 20; x++) {
        drops[x] = 1
      }
      
      const draw = () => {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.05)'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        
        ctx.fillStyle = 'rgba(0, 255, 0, 0.3)'
        ctx.font = '15px monospace'
        
        for (let i = 0; i < drops.length; i++) {
          const text = charArray[Math.floor(Math.random() * charArray.length)]
          ctx.fillText(text, i * 20, drops[i] * 20)
          
          if (drops[i] * 20 > canvas.height && Math.random() > 0.975) {
            drops[i] = 0
          }
          drops[i]++
        }
      }
      
      setInterval(draw, 35)
    },

    // 获取流星样式
    getMeteorStyle() {
      return {
        left: Math.random() * 100 + '%',
        animationDelay: Math.random() * 5 + 's',
        animationDuration: (Math.random() * 3 + 2) + 's'
      }
    },

    // 获取安全得分
    getSecurityScore() {
      if (this.passwords.length === 0) return 0
      
      const scores = this.passwords.map(p => this.getPasswordStrength(p.password))
      const average = scores.reduce((sum, score) => sum + score, 0) / scores.length
      return Math.round(average)
    },

    // 获取密码强度
    getPasswordStrength(password) {
      if (!password) return 0
      
      let strength = 0
      
      // 长度评分
      if (password.length >= 8) strength += 20
      if (password.length >= 12) strength += 10
      if (password.length >= 16) strength += 10
      
      // 字符类型评分
      if (/[a-z]/.test(password)) strength += 15
      if (/[A-Z]/.test(password)) strength += 15
      if (/\d/.test(password)) strength += 15
      if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 15
      
      return Math.min(strength, 100)
    },

    // 获取密码强度等级
    getSecurityLevel(password) {
      const strength = this.getPasswordStrength(password)
      if (strength >= 80) return 'high'
      if (strength >= 60) return 'medium'
      return 'low'
    },

    // 获取密码强度文本
    getPasswordStrengthText(password) {
      const level = this.getSecurityLevel(password)
      return {
        high: '强',
        medium: '中',
        low: '弱'
      }[level] || '无'
    },

    // 获取密码强度样式类
    getPasswordStrengthClass(password) {
      return 'strength-' + this.getSecurityLevel(password)
    },

    // 获取安全文本
    getSecurityText(password) {
      return this.getPasswordStrengthText(password)
    },

    // 获取分类图标
    getCategoryIcon(category) {
      const iconMap = {
        social: 'el-icon-chat-dot-round',
        email: 'el-icon-message',
        work: 'el-icon-suitcase',
        shopping: 'el-icon-shopping-cart-2',
        entertainment: 'el-icon-video-play',
        finance: 'el-icon-coin',
        other: 'el-icon-more'
      }
      return iconMap[category] || 'el-icon-link'
    },

    // 获取分类名称
    getCategoryName(category) {
      const nameMap = {
        social: '社交',
        email: '邮箱',
        work: '工作',
        shopping: '购物',
        entertainment: '娱乐',
        finance: '金融',
        other: '其他'
      }
      return nameMap[category] || '其他'
    },

    // 获取头像渐变色
    getAvatarGradient(category) {
      const gradients = {
        social: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        email: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        work: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        shopping: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        entertainment: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        finance: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        other: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
      }
      return gradients[category] || gradients.other
    },

    // 获取连接状态
    getConnectionStatus(password) {
      // 模拟连接状态
      const statuses = ['online', 'offline', 'warning']
      return statuses[password.id % 3]
    },

    // 格式化URL
    formatUrl(url) {
      try {
        const domain = new URL(url).hostname
        return domain.replace('www.', '')
      } catch {
        return url
      }
    },

    // 格式化日期
    formatDate(date) {
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      if (days < 30) return `${Math.floor(days/7)}周前`
      if (days < 365) return `${Math.floor(days/30)}个月前`
      return `${Math.floor(days/365)}年前`
    },

    // 获取过滤数量
    getFilterCount(category) {
      if (!category) return this.passwords.length
      return this.passwords.filter(p => p.category === category).length
    },

    // 排序密码
    sortPasswords(passwords) {
      const sortMap = {
        name: (a, b) => a.siteName.localeCompare(b.siteName),
        date: (a, b) => b.updatedAt - a.updatedAt,
        security: (a, b) => this.getPasswordStrength(b.password) - this.getPasswordStrength(a.password),
        usage: (a, b) => (b.usageCount || 0) - (a.usageCount || 0)
      }
      
      return [...passwords].sort(sortMap[this.currentSort] || sortMap.name)
    },

    // 处理排序改变
    handleSortChange(command) {
      this.currentSort = command
    },

    // 获取排序标签
    getSortLabel(sort) {
      const labelMap = {
        name: '按名称',
        date: '按时间',
        security: '按安全性',
        usage: '按使用率'
      }
      return labelMap[sort] || '排序'
    },

    // 更新搜索建议
    updateSearchSuggestions() {
      const keyword = this.searchKeyword.toLowerCase()
      this.searchSuggestions = this.passwords
        .filter(p => 
          p.siteName.toLowerCase().includes(keyword) ||
          p.username.toLowerCase().includes(keyword)
        )
        .slice(0, 5)
    },

    // 选择搜索建议
    selectSuggestion(suggestion) {
      this.selectedPasswordId = suggestion.id
      this.searchKeyword = suggestion.siteName
      this.showSearchSuggestions = false
    },

    // 隐藏搜索建议
    hideSearchSuggestions() {
      setTimeout(() => {
        this.showSearchSuggestions = false
      }, 200)
    },

    // 更新站点名称建议
    updateSiteNameSuggestions(value) {
      const commonSites = [
        'Gmail', 'GitHub', 'Facebook', 'Twitter', 'Instagram', 'LinkedIn',
        'Amazon', '淘宝', '京东', '微信', 'QQ', '支付宝', 'Netflix', 'YouTube',
        '百度', '腾讯', '阿里巴巴', 'Microsoft', 'Apple', 'Google'
      ]
      
      this.siteNameSuggestions = commonSites
        .filter(site => site.toLowerCase().includes(value.toLowerCase()))
        .slice(0, 5)
    },

    // 选择站点名称建议
    selectSiteNameSuggestion(suggestion) {
      this.passwordForm.siteName = suggestion
      this.siteNameSuggestions = []
    },

    // 选择密码
    selectPassword(id) {
      this.selectedPasswordId = this.selectedPasswordId === id ? null : id
    },

    // 切换密码可见性
    togglePasswordVisibility(id) {
      const password = this.passwords.find(p => p.id === id)
      if (password) {
        this.$set(password, 'showPassword', !password.showPassword)
        
        // 10秒后自动隐藏
        if (password.showPassword) {
          setTimeout(() => {
            this.$set(password, 'showPassword', false)
          }, 10000)
        }
      }
    },

    // 切换收藏状态
    toggleFavorite(id) {
      const password = this.passwords.find(p => p.id === id)
      if (password) {
        password.isFavorite = !password.isFavorite
        this.$message({
          message: password.isFavorite ? '已添加到收藏' : '已从收藏中移除',
          type: 'success'
        })
      }
    },

    // 复制到剪贴板
    async copyToClipboard(text, type) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message({
          message: `${type}已复制到剪贴板`,
          type: 'success'
        })
      } catch (err) {
        this.$message({
          message: '复制失败，请手动复制',
          type: 'error'
        })
      }
    },

    // 复制密码
    copyPassword(password) {
      this.copyToClipboard(password.password, '密码')
      // 增加使用次数
      password.usageCount = (password.usageCount || 0) + 1
    },

    // 打开网站
    openWebsite(url) {
      if (url) {
        window.open(url.startsWith('http') ? url : 'https://' + url, '_blank')
      }
    },

    // 处理卡片操作
    handleCardAction(command, password) {
      switch (command) {
        case 'edit':
          this.editPassword(password)
          break
        case 'duplicate':
          this.duplicatePassword(password)
          break
        case 'export':
          this.exportPassword(password)
          break
        case 'delete':
          this.deletePassword(password.id)
          break
      }
    },

    // 编辑密码
    editPassword(password) {
      this.editingItem = password
      this.passwordForm = { ...password }
      this.showAddDialog = true
    },

    // 复制密码条目
    duplicatePassword(password) {
      const newPassword = {
        ...password,
        id: Date.now(),
        siteName: password.siteName + ' (副本)',
        createdAt: new Date(),
        updatedAt: new Date(),
        usageCount: 0
      }
      
      this.passwords.push(newPassword)
      this.$message({
        message: '密码已复制',
        type: 'success'
      })
    },

    // 删除密码
    deletePassword(id) {
      this.$confirm('确定要删除这个密码吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.passwords.findIndex(p => p.id === id)
        if (index !== -1) {
          this.passwords.splice(index, 1)
          this.$message({
            type: 'success',
            message: '删除成功'
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 快速编辑
    quickEdit(password) {
      this.editPassword(password)
    },

    // 分享密码
    sharePassword(password) {
      // 生成分享链接或二维码
      this.$message({
        message: '分享功能开发中...',
        type: 'info'
      })
    },

    // 生成随机密码
    generateRandomPassword() {
      const options = this.generatorOptions
      let charset = ''
      
      if (options.includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz'
      if (options.includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      if (options.includeNumbers) charset += '0123456789'
      if (options.includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?'
      
      if (options.excludeSimilar) {
        charset = charset.replace(/[0oO1lI]/g, '')
      }
      
      let password = ''
      for (let i = 0; i < options.length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length))
      }
      
      this.generatedPassword = password
      
      if (this.showAddDialog) {
        this.passwordForm.password = password
      }
    },

    // 保存密码
    savePassword() {
      this.$refs.passwordForm.validate(valid => {
        if (valid) {
          this.saving = true
          
          setTimeout(() => {
            if (this.editingItem) {
              // 更新现有密码
              const index = this.passwords.findIndex(p => p.id === this.editingItem.id)
              if (index !== -1) {
                this.$set(this.passwords, index, {
                  ...this.passwordForm,
                  id: this.editingItem.id,
                  createdAt: this.editingItem.createdAt,
                  updatedAt: new Date(),
                  usageCount: this.editingItem.usageCount || 0
                })
              }
            } else {
              // 添加新密码
              this.passwords.push({
                ...this.passwordForm,
                id: Date.now(),
                showPassword: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                usageCount: 0
              })
            }
            
            this.handleDialogClose()
            this.$message({
              type: 'success',
              message: this.editingItem ? '密码已更新' : '密码已保存'
            })
          }, 1000)
        }
      })
    },

    // 关闭对话框
    handleDialogClose() {
      this.showAddDialog = false
      this.editingItem = null
      this.showFormPassword = false
      this.saving = false
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.passwordForm = {
        siteName: '',
        username: '',
        password: '',
        website: '',
        category: 'other',
        isFavorite: false,
        notes: '',
        enableAutoFill: false
      }
      this.$nextTick(() => {
        this.$refs.passwordForm && this.$refs.passwordForm.clearValidate()
      })
    },

    // 处理站点名称变化
    handleSiteNameChange(value) {
      // 可以根据站点名称自动推断分类
      const categoryMap = {
        'gmail': 'email',
        'outlook': 'email',
        '163': 'email',
        'qq': 'email',
        'github': 'work',
        'gitlab': 'work',
        'linkedin': 'work',
        'facebook': 'social',
        'twitter': 'social',
        'instagram': 'social',
        '微信': 'social',
        'amazon': 'shopping',
        '淘宝': 'shopping',
        '京东': 'shopping',
        'netflix': 'entertainment',
        'youtube': 'entertainment',
        '支付宝': 'finance'
      }
      
      const lowerName = value.toLowerCase()
      for (const [key, category] of Object.entries(categoryMap)) {
        if (lowerName.includes(key)) {
          this.passwordForm.category = category
          break
        }
      }
    },

    // 处理密码变化
    handlePasswordChange(value) {
      // 实时更新密码强度
      this.$forceUpdate()
    },

    // 测试网站连接
    testWebsite() {
      if (this.passwordForm.website) {
        this.openWebsite(this.passwordForm.website)
      }
    },

    // 生成用户名
    generateUsername() {
      const suggestions = [
        'user_' + Math.random().toString(36).substr(2, 8),
        'guest_' + Date.now().toString().substr(-6),
        this.passwordForm.siteName.toLowerCase() + '_user'
      ]
      
      this.passwordForm.username = suggestions[Math.floor(Math.random() * suggestions.length)]
    },

    // 获取空状态标题
    getEmptyStateTitle() {
      if (this.searchKeyword) {
        return '没有找到相关密码'
      }
      if (this.selectedCategory) {
        return '该分类暂无密码'
      }
      return '开始管理您的密码'
    },

    // 获取空状态描述
    getEmptyStateDescription() {
      if (this.searchKeyword) {
        return '试试修改搜索关键词或清除筛选条件'
      }
      if (this.selectedCategory) {
        return '在其他分类中查看或添加新的密码'
      }
      return '安全地存储和管理您的所有密码'
    },

    // 自动隐藏密码
    startAutoHidePasswords() {
      setInterval(() => {
        this.passwords.forEach(password => {
          if (password.showPassword) {
            this.$set(password, 'showPassword', false)
          }
        })
      }, 30000) // 30秒自动隐藏所有密码
    },

    // 快速添加
    quickAdd() {
      this.showAddDialog = true
    },

    // 打开密码生成器
    openPasswordGenerator() {
      this.showGeneratorDialog = true
      this.generateRandomPassword()
    },

    // 打开AI助手
    openAIAssistant() {
      this.$message({
        message: 'AI助手功能即将推出！',
        type: 'info'
      })
    },

    // 语音搜索
    startVoiceSearch() {
      if ('webkitSpeechRecognition' in window) {
        const recognition = new webkitSpeechRecognition()
        recognition.lang = 'zh-CN'
        recognition.onresult = (event) => {
          this.searchKeyword = event.results[0][0].transcript
        }
        recognition.start()
      } else {
        this.$message({
          message: '您的浏览器不支持语音搜索',
          type: 'warning'
        })
      }
    },

    // 显示AI搜索
    showAISearch() {
      this.$message({
        message: 'AI智能搜索功能开发中...',
        type: 'info'
      })
    },

    // 复制生成的密码
    copyGeneratedPassword() {
      this.copyToClipboard(this.generatedPassword, '生成的密码')
    },

    // 重新生成密码
    regeneratePassword() {
      this.generateRandomPassword()
    }
  }
}
</script>

<style lang="scss" scoped>
.password-manager {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  // 3D立体背景
  .background-3d {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: radial-gradient(ellipse at top, #667eea 0%, #764ba2 100%);
    overflow: hidden;

    // 流星雨效果
    .meteor-shower {
      position: absolute;
      width: 100%;
      height: 100%;

      .meteor {
        position: absolute;
        width: 2px;
        height: 80px;
        background: linear-gradient(to bottom, transparent, #fff, transparent);
        animation: meteor-fall linear infinite;
        
        @keyframes meteor-fall {
          0% {
            transform: translateY(-100px) translateX(-100px) rotate(45deg);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: translateY(calc(100vh + 100px)) translateX(calc(100vw + 100px)) rotate(45deg);
            opacity: 0;
          }
        }
      }
    }

    // 几何图形动画
    .geometric-shapes {
      position: absolute;
      width: 100%;
      height: 100%;

      .shape-orbit {
        position: absolute;
        top: 20%;
        left: 20%;
        width: 300px;
        height: 300px;

        .orbit-ring {
          position: absolute;
          border: 2px solid rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          animation: rotate linear infinite;

          &.ring-1 {
            width: 100%;
            height: 100%;
            animation-duration: 20s;
          }

          &.ring-2 {
            width: 80%;
            height: 80%;
            top: 10%;
            left: 10%;
            animation-duration: 15s;
            animation-direction: reverse;
          }

          &.ring-3 {
            width: 60%;
            height: 60%;
            top: 20%;
            left: 20%;
            animation-duration: 25s;
          }
        }

        .central-core {
          position: absolute;
          top: 45%;
          left: 45%;
          width: 30px;
          height: 30px;
                    background: radial-gradient(circle, #4facfe 0%, #00f2fe 100%);
          border-radius: 50%;
          box-shadow: 0 0 30px rgba(79, 172, 254, 0.5);
          animation: pulse 2s ease-in-out infinite;
        }

        @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.2); opacity: 0.8; }
        }
      }

      .floating-cubes {
        position: absolute;
        width: 100%;
        height: 100%;

        .cube {
          position: absolute;
          width: 40px;
          height: 40px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          animation: float linear infinite;

          &.cube-1 {
            top: 60%;
            left: 70%;
            animation-duration: 12s;
          }

          &.cube-2 {
            top: 30%;
            right: 20%;
            animation-duration: 18s;
            animation-delay: -6s;
          }

          &.cube-3 {
            bottom: 20%;
            left: 10%;
            animation-duration: 15s;
            animation-delay: -3s;
          }

          @keyframes float {
            0%, 100% {
              transform: translateY(0px) rotate(0deg);
            }
            25% {
              transform: translateY(-20px) rotate(90deg);
            }
            50% {
              transform: translateY(-40px) rotate(180deg);
            }
            75% {
              transform: translateY(-20px) rotate(270deg);
            }
          }
        }
      }
    }

    // 光波扩散效果
    .light-waves {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);

      .wave {
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translateX(-50%);
        animation: wave-expand 4s ease-out infinite;

        &.wave-2 {
          animation-delay: 1.3s;
        }

        &.wave-3 {
          animation-delay: 2.6s;
        }

        @keyframes wave-expand {
          0% {
            transform: translateX(-50%) scale(0);
            opacity: 0.8;
          }
          50% {
            opacity: 0.4;
          }
          100% {
            transform: translateX(-50%) scale(3);
            opacity: 0;
          }
        }
      }
    }

    // 数字雨背景
    .matrix-rain {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.1;
      pointer-events: none;

      canvas {
        width: 100%;
        height: 100%;
      }
    }
  }

  // 玻璃拟态样式
  .glass-premium {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 
      0 10px 40px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  // 主容器
  .main-container {
    position: relative;
    z-index: 1;
    margin: 0 auto;
    max-width: 1400px;
    padding: 20px;
  }

  // 超级导航栏
  .super-nav {
    border-radius: 20px;
    padding: 20px 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;

    .nav-left {
      display: flex;
      align-items: center;
      gap: 40px;
      flex-wrap: wrap;

      .brand-container {
        display: flex;
        align-items: center;
        gap: 15px;

        .brand-logo {
          position: relative;
          width: 60px;
          height: 60px;

          .logo-rings {
            position: absolute;
            width: 100%;
            height: 100%;

            .ring {
              position: absolute;
              border: 2px solid rgba(255, 255, 255, 0.3);
              border-radius: 50%;
              animation: logo-spin linear infinite;

              &.ring-outer {
                width: 100%;
                height: 100%;
                animation-duration: 20s;
              }

              &.ring-middle {
                width: 80%;
                height: 80%;
                top: 10%;
                left: 10%;
                animation-duration: 15s;
                animation-direction: reverse;
              }

              &.ring-inner {
                width: 60%;
                height: 60%;
                top: 20%;
                left: 20%;
                animation-duration: 10s;
              }
            }
          }

          .logo-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
            animation: logo-pulse 2s ease-in-out infinite;
          }

          @keyframes logo-spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }

          @keyframes logo-pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.1); }
          }
        }

        .brand-info {
          .brand-name {
            font-size: 28px;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 0;
            letter-spacing: -0.5px;
          }

          .brand-tagline {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            margin-top: 2px;
          }
        }
      }

      .nav-menu {
        display: flex;
        gap: 10px;

        .menu-item {
          padding: 12px 20px;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateY(-2px);
          }

          &.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.3);
          }

          i {
            font-size: 16px;
          }
        }
      }
    }

    .nav-right {
      display: flex;
      align-items: center;
      gap: 15px;

      .security-score {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px 15px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        color: white;

        .score-ring {
          position: relative;
          width: 40px;
          height: 40px;

          .score-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(
              from 0deg,
              #4facfe 0deg,
              #00f2fe calc(var(--score) * 3.6deg),
              rgba(255, 255, 255, 0.2) calc(var(--score) * 3.6deg)
            );
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              width: 30px;
              height: 30px;
              background: rgba(255, 255, 255, 0.1);
              border-radius: 50%;
              backdrop-filter: blur(10px);
            }
          }

          .score-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: bold;
            color: white;
            z-index: 1;
          }
        }

        .score-info {
          .score-label {
            font-size: 12px;
            opacity: 0.7;
            margin: 0;
          }

          .score-value {
            font-size: 16px;
            font-weight: bold;
            margin: 0;
          }
        }
      }

      .nav-actions {
        display: flex;
        gap: 10px;

        .action-btn {
          width: 45px;
          height: 45px;
          border: none;
          border-radius: 12px;
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.8);
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);

            &::before {
              left: 100%;
            }
          }

          &.primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;

            &:hover {
              transform: translateY(-2px) scale(1.05);
              box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
            }
          }
        }
      }
    }
  }

  // 控制面板
  .control-panel {
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .panel-title {
        font-size: 24px;
        font-weight: 700;
        color: white;
        margin: 0;
      }

      .panel-stats {
        display: flex;
        gap: 20px;
        color: rgba(255, 255, 255, 0.7);

        .stat-item {
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }
    }

    .panel-content {
      display: grid;
      grid-template-columns: 1fr auto;
      gap: 20px;
      align-items: center;

      .search-section {
        .search-container {
          position: relative;

          .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.08);
            color: white;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;

            &::placeholder {
              color: rgba(255, 255, 255, 0.5);
            }

            &:focus {
              border-color: rgba(79, 172, 254, 0.5);
              background: rgba(255, 255, 255, 0.12);
              box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.1);
            }
          }

          .search-actions {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 5px;

            .search-btn {
              width: 35px;
              height: 35px;
              border: none;
              border-radius: 8px;
              background: rgba(255, 255, 255, 0.1);
              color: rgba(255, 255, 255, 0.7);
              cursor: pointer;
              transition: all 0.2s ease;
              display: flex;
              align-items: center;
              justify-content: center;

              &:hover {
                background: rgba(255, 255, 255, 0.2);
                color: white;
              }
            }
          }

          .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            margin-top: 5px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            z-index: 100;
            max-height: 300px;
            overflow-y: auto;

            .suggestion-item {
              padding: 12px 16px;
              cursor: pointer;
              transition: background 0.2s ease;
              display: flex;
              align-items: center;
              gap: 12px;
              color: #333;

              &:hover {
                background: rgba(79, 172, 254, 0.1);
              }

              .suggestion-icon {
                width: 32px;
                height: 32px;
                border-radius: 8px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 14px;
              }

              .suggestion-info {
                flex: 1;

                .suggestion-name {
                  font-weight: 600;
                  margin-bottom: 2px;
                }

                .suggestion-detail {
                  font-size: 12px;
                  color: #666;
                }
              }
            }
          }
        }
      }

      .filter-section {
        display: flex;
        gap: 15px;
        align-items: center;

        .filter-group {
          display: flex;
          gap: 10px;
          align-items: center;

          .filter-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            white-space: nowrap;
          }
        }
      }
    }
  }

  // 分类过滤器
  .category-filter {
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 30px;

    .category-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .category-item {
        padding: 10px 18px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 500;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          transition: left 0.5s;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

          &::before {
            left: 100%;
          }
        }

        &.active {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          color: white;
          border-color: transparent;
          box-shadow: 0 6px 20px rgba(79, 172, 254, 0.3);
        }

        .category-icon {
          font-size: 16px;
        }

        .category-count {
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: bold;
          margin-left: 5px;
        }
      }
    }
  }

  // 密码网格
  .password-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 25px;
    margin-bottom: 30px;

    .password-card {
      border-radius: 20px;
      padding: 25px;
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      cursor: pointer;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
      }

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);

        &::before {
          transform: scaleX(1);
        }
      }

      &.selected {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(79, 172, 254, 0.3);
        border: 2px solid rgba(79, 172, 254, 0.5);

        &::before {
          transform: scaleX(1);
        }
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;

        .site-avatar {
          width: 60px;
          height: 60px;
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 24px;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          position: relative;
          overflow: hidden;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
          }

          .security-indicator {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid white;
            
            &.high {
              background: #52c41a;
              box-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
            }
            
            &.medium {
              background: #faad14;
              box-shadow: 0 0 10px rgba(250, 173, 20, 0.5);
            }
            
            &.low {
              background: #ff4d4f;
              box-shadow: 0 0 10px rgba(255, 77, 79, 0.5);
            }
          }

          .connection-status {
            position: absolute;
            bottom: -3px;
            right: -3px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;

            &.online {
              background: #52c41a;
              animation: pulse-online 2s infinite;
            }

            &.offline {
              background: #ff4d4f;
            }

            &.warning {
              background: #faad14;
              animation: pulse-warning 1s infinite;
            }

            @keyframes pulse-online {
              0% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7); }
              70% { box-shadow: 0 0 0 10px rgba(82, 196, 26, 0); }
              100% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0); }
            }

            @keyframes pulse-warning {
              0%, 100% { opacity: 1; }
              50% { opacity: 0.5; }
            }
          }
        }

        .card-actions {
          display: flex;
          gap: 8px;
          opacity: 0;
          transition: opacity 0.3s ease;

          .action-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              color: white;
              transform: scale(1.1);
            }
          }

          .favorite-btn {
            color: rgba(255, 255, 255, 0.6);
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              color: #ffd700;
              transform: scale(1.2);
            }
            
            &.active {
              color: #ffd700;
              animation: heartbeat 1s ease-in-out infinite;
              
              @keyframes heartbeat {
                0% { transform: scale(1); }
                14% { transform: scale(1.3); }
                28% { transform: scale(1); }
                42% { transform: scale(1.3); }
                70% { transform: scale(1); }
              }
            }
          }
        }
      }

      &:hover .card-actions {
        opacity: 1;
      }

      .card-content {
        .site-info {
          margin-bottom: 20px;
          
          .site-name {
            font-size: 20px;
            font-weight: 700;
            color: white;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
          }
          
          .site-url {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            transition: color 0.3s ease;

            &:hover {
              color: #4facfe;
            }
          }

          .site-category {
            display: inline-block;
            padding: 4px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 8px;
          }
        }
        
        .credentials {
          margin-bottom: 20px;
          
          .credential-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            
            &:last-child {
              border-bottom: none;
            }
            
            .credential-info {
              flex: 1;
              min-width: 0;
              
              .credential-label {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
                margin-bottom: 4px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }
              
              .credential-value {
                font-size: 16px;
                color: white;
                font-weight: 500;
                font-family: 'Monaco', 'Menlo', monospace;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                
                &.masked {
                  font-family: inherit;
                  letter-spacing: 2px;
                }
              }
            }
            
            .credential-actions {
              display: flex;
              gap: 5px;
              margin-left: 10px;
              
              .action-btn {
                width: 32px;
                height: 32px;
                border: none;
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.7);
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                
                &:hover {
                  background: rgba(255, 255, 255, 0.2);
                  color: white;
                  transform: scale(1.1);
                }
              }
            }
          }
        }

        .password-strength {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 15px;

          .strength-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .strength-bar {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            position: relative;

            .strength-fill {
              height: 100%;
              border-radius: 3px;
              transition: all 0.5s ease;
              background: linear-gradient(90deg, #ff4d4f 0%, #faad14 50%, #52c41a 100%);
              
              &.strength-low {
                background: #ff4d4f;
              }
              
              &.strength-medium {
                background: #faad14;
              }
              
              &.strength-high {
                background: #52c41a;
              }
            }
          }

          .strength-text {
            font-size: 12px;
            font-weight: 600;
            color: white;
            min-width: 30px;
            text-align: right;
          }
        }
        
        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 15px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          
          .usage-stats {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            
            .stat-item {
              display: flex;
              align-items: center;
              gap: 4px;
            }
          }
          
          .quick-actions {
            display: flex;
            gap: 8px;
            
            .quick-btn {
              padding: 6px 12px;
                            border: 1px solid rgba(255, 255, 255, 0.2);
              border-radius: 8px;
              background: rgba(255, 255, 255, 0.05);
              color: rgba(255, 255, 255, 0.8);
              cursor: pointer;
              transition: all 0.3s ease;
              font-size: 12px;
              display: flex;
              align-items: center;
              gap: 4px;
              
              &:hover {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border-color: rgba(255, 255, 255, 0.3);
                transform: translateY(-1px);
              }
              
              &.primary {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                border-color: transparent;
                color: white;
                font-weight: 600;
                
                &:hover {
                  transform: translateY(-1px) scale(1.05);
                  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
                }
              }
            }
          }
        }
      }
    }
  }

  // 浮动操作按钮
  .floating-action-panel {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    
    .fab-main {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
        border-radius: 50%;
      }
      
      &:hover {
        transform: scale(1.1) rotate(180deg);
        box-shadow: 0 12px 35px rgba(79, 172, 254, 0.6);
      }
      
      &:active {
        transform: scale(0.95);
      }
    }
    
    .fab-menu {
      position: absolute;
      bottom: 70px;
      right: 0;
      display: flex;
      flex-direction: column;
      gap: 15px;
      opacity: 0;
      visibility: hidden;
      transform: translateY(20px);
      transition: all 0.3s ease;
      
      &.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }
      
      .fab-item {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        font-size: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        
        &:hover {
          background: rgba(255, 255, 255, 0.25);
          transform: scale(1.1);
        }
        
        &::after {
          content: attr(data-tooltip);
          position: absolute;
          right: 60px;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 8px 12px;
          border-radius: 6px;
          font-size: 12px;
          white-space: nowrap;
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
          pointer-events: none;
        }
        
        &:hover::after {
          opacity: 1;
          visibility: visible;
        }
      }
    }
  }

  // 对话框样式
  .password-dialog {
    .el-dialog {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
      
      .el-dialog__header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: -20px -20px 20px -20px;
        padding: 20px;
        border-radius: 20px 20px 0 0;
        
        .el-dialog__title {
          color: white;
          font-size: 20px;
          font-weight: 700;
        }
        
        .el-dialog__headerbtn {
          .el-dialog__close {
            color: white;
            font-size: 20px;
            
            &:hover {
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }
      }
      
      .el-dialog__body {
        padding: 20px;
        
        .form-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
          margin-bottom: 20px;
          
          .form-item {
            .form-label {
              display: block;
              margin-bottom: 8px;
              font-weight: 600;
              color: #333;
              font-size: 14px;
            }
            
            .form-input {
              width: 100%;
              padding: 12px 16px;
              border: 2px solid #e0e0e0;
              border-radius: 10px;
              font-size: 14px;
              transition: all 0.3s ease;
              
              &:focus {
                outline: none;
                border-color: #4facfe;
                box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
              }
            }
            
            .form-textarea {
              width: 100%;
              padding: 12px 16px;
              border: 2px solid #e0e0e0;
              border-radius: 10px;
              font-size: 14px;
              resize: vertical;
              min-height: 100px;
              transition: all 0.3s ease;
              
              &:focus {
                outline: none;
                border-color: #4facfe;
                box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
              }
            }
            
            .form-select {
              width: 100%;
              padding: 12px 16px;
              border: 2px solid #e0e0e0;
              border-radius: 10px;
              font-size: 14px;
              background: white;
              cursor: pointer;
              transition: all 0.3s ease;
              
              &:focus {
                outline: none;
                border-color: #4facfe;
                box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
              }
            }
            
            &.full-width {
              grid-column: span 2;
            }
          }
        }
        
        .password-input-group {
          position: relative;
          
          .password-input {
            padding-right: 100px;
          }
          
          .password-actions {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 5px;
            
            .password-action-btn {
              width: 32px;
              height: 32px;
              border: none;
              border-radius: 6px;
              background: #f0f0f0;
              color: #666;
              cursor: pointer;
              transition: all 0.3s ease;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              
              &:hover {
                background: #e0e0e0;
                color: #333;
              }
              
              &.generate {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
                
                &:hover {
                  transform: scale(1.05);
                  box-shadow: 0 2px 10px rgba(79, 172, 254, 0.3);
                }
              }
            }
          }
        }
      }
      
      .el-dialog__footer {
        text-align: right;
        padding: 20px;
        border-top: 1px solid #f0f0f0;
        
        .dialog-btn {
          padding: 12px 24px;
          border: none;
          border-radius: 10px;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-left: 10px;
          
          &.cancel {
            background: #f0f0f0;
            color: #666;
            
            &:hover {
              background: #e0e0e0;
              color: #333;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
            }
            
            &:disabled {
              background: #ccc;
              cursor: not-allowed;
              transform: none;
              box-shadow: none;
            }
          }
        }
      }
    }
  }

  // 密码生成器对话框
  .generator-dialog {
    .password-generator-advanced {
      .generator-result {
        margin-bottom: 30px;
        
        .generated-password {
          position: relative;
          margin-bottom: 20px;
          
          .password-output {
            width: 100%;
            padding: 15px 60px 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            background: #f8f9fa;
            color: #333;
            font-weight: 600;
            letter-spacing: 1px;
            
            &:focus {
              outline: none;
              border-color: #4facfe;
              box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            }
          }
          
          .result-actions {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 5px;
            
            .action-btn {
              width: 36px;
              height: 36px;
              border: none;
              border-radius: 8px;
              background: #4facfe;
              color: white;
              cursor: pointer;
              transition: all 0.3s ease;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 16px;
              
              &:hover {
                background: #00f2fe;
                transform: scale(1.1);
              }
            }
          }
        }
        
        .generation-options {
          .option-group {
            margin-bottom: 25px;
            
            label {
              display: block;
              margin-bottom: 10px;
              font-weight: 600;
              color: #333;
            }
            
            .el-slider {
              margin: 10px 0;
            }
          }
          
          .option-checkboxes {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            
            .el-checkbox {
              margin-right: 0;
              
              .el-checkbox__label {
                font-weight: 500;
                color: #333;
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .password-grid {
      grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
      gap: 20px;
    }
  }

  @media (max-width: 768px) {
    .main-container {
      padding: 15px;
    }
    
    .super-nav {
      padding: 15px 20px;
      
      .nav-left {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
      }
      
      .nav-right {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        width: 100%;
      }
    }
    
    .control-panel {
      .panel-content {
        grid-template-columns: 1fr;
        gap: 15px;
      }
    }
    
    .category-filter {
      .category-list {
        justify-content: center;
      }
    }
    
    .password-grid {
      grid-template-columns: 1fr;
      gap: 15px;
    }
    
    .floating-action-panel {
      bottom: 20px;
      right: 20px;
      
      .fab-main {
        width: 55px;
        height: 55px;
      }
    }
    
    .password-dialog {
      .el-dialog {
        width: 90%;
        margin: 0 auto;
        
        .form-grid {
          grid-template-columns: 1fr;
          gap: 15px;
          
          .form-item {
            &.full-width {
              grid-column: span 1;
            }
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .main-container {
      padding: 10px;
    }
    
    .super-nav {
      padding: 15px;
      
      .brand-container {
        .brand-logo {
          width: 50px;
          height: 50px;
          
          .logo-center {
            width: 25px;
            height: 25px;
            font-size: 14px;
          }
        }
        
        .brand-info {
          .brand-name {
            font-size: 24px;
          }
          
          .brand-tagline {
            font-size: 12px;
          }
        }
      }
    }
    
    .control-panel {
      padding: 20px;
      
      .panel-header {
        .panel-title {
          font-size: 20px;
        }
      }
    }
    
    .password-card {
      padding: 20px;
      
      .card-header {
        .site-avatar {
          width: 50px;
          height: 50px;
          font-size: 20px;
        }
      }
      
      .site-info {
        .site-name {
          font-size: 18px;
        }
      }
    }
  }
}

// 全局动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, 100% {
    transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }
  0% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }
  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    transform: scale3d(.9, .9, .9);
  }
  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    transform: scale3d(.97, .97, .97);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

// 进入动画
.password-card {
  animation: fadeInUp 0.6s ease-out;
}

.password-card:nth-child(2n) {
  animation-delay: 0.1s;
}

.password-card:nth-child(3n) {
  animation-delay: 0.2s;
}

.password-card:nth-child(4n) {
  animation-delay: 0.3s;
}

// 组件进入动画
.control-panel {
  animation: slideInRight 0.8s ease-out;
}

.category-filter {
  animation: slideInRight 0.8s ease-out 0.2s both;
}

.floating-action-panel {
  animation: bounceIn 1s ease-out 0.5s both;
}

// 工具提示样式
.tooltip {
  position: relative;
  
  &::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 1000;
  }
  
  &:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
  }
}

// 加载动画
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  
  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}
</style>



