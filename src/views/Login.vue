<template>
  <div class="login-container">
    <!-- 系统标题 -->
    <h1 class="system-title">
      <svg
        class="title-icon"
        viewBox="0 0 64 64"
        xmlns="http://www.w3.org/2000/svg"
        @click="handleIconClick"
      >
        <!-- 主图标 -->
        <path
          class="icon-main"
          d="M32 12L12 32l20 20 20-20L32 12zm0 8.97l12.03 12.03L32 45.03 19.97 32.97 32 20.97z"
        />

        <!-- 动态光环 -->
        <circle
          class="halo"
          cx="32"
          cy="32"
          r="28"
          fill="none"
          stroke-width="2"
        />
      </svg>
      <span class="title-text">
        {{ isWelcomeTitle ? "欢迎来到开放世界" : "OPEN-WORLD" }}</span
      >
      <div class="title-underline" />
    </h1>

    <!-- 添加页面切换过渡 -->
    <transition :css="false" @enter="enterTransition" @leave="leaveTransition">
      <!-- 主登录注册框 -->
      <div v-if="!showForgotPassword" key="main" class="main-box">
        <!-- 切换按钮 -->
        <div class="switch-buttons">
          <div
            class="switch-item"
            :class="{ active: isLogin }"
            @click="switchForm('login')"
          >
            登录
            <div class="underline" />
          </div>
          <div
            class="switch-item"
            :class="{ active: !isLogin }"
            @click="switchForm('register')"
          >
            注册
            <div class="underline" />
          </div>
        </div>

        <!-- 表单容器 -->
        <div class="form-container">
          <!-- <transition :name="transitionName" mode="out-in"> -->
          <div v-if="isLogin" key="1">
            <!-- 登录表单 -->
            <el-form
              ref="loginForm"
              :model="loginData"
              :rules="rules"
              class="login-form"
            >
              <el-form-item prop="username">
                <el-input
                  v-model="loginData.username"
                  prefix-icon="el-icon-user"
                  placeholder="用户名"
                  class="custom-input"
                />
              </el-form-item>

              <el-form-item prop="loginPassword">
                <el-input
                  v-model="loginData.loginPassword"
                  prefix-icon="el-icon-lock"
                  placeholder="密码"
                  show-password
                  class="custom-input"
                  @keyup.enter.native="handleLogin"
                />
              </el-form-item>

              <div class="remember-me">
                <el-checkbox v-model="rememberMe" class="custom-checkbox">
                  <span class="remember-text">记住密码</span>
                </el-checkbox>
                <el-link
                  type="primary"
                  class="forgot-password"
                  @click="startForgotPassword"
                >
                  忘记密码?
                </el-link>
              </div>

              <el-button
                type="primary"
                class="submit-btn gradient-btn"
                :loading="loading"
                @click="handleLogin"
              >
                {{ loading ? "登录中..." : "立即登录" }}
              </el-button>
            </el-form>
          </div>
          <div v-else key="2">
            <!-- 注册表单 -->
            <el-form
              ref="registerForm"
              :model="form"
              :rules="rules"
              class="register-form"
            >
              <el-form-item prop="username">
                <el-input
                  v-model="form.username"
                  prefix-icon="el-icon-user"
                  placeholder="用户名"
                  class="custom-input"
                />
              </el-form-item>

              <el-form-item prop="email">
                <el-input
                  v-model="form.email"
                  prefix-icon="el-icon-message"
                  placeholder="邮箱"
                  class="custom-input"
                />
              </el-form-item>

              <el-form-item prop="password">
                <el-input
                  v-model="form.password"
                  prefix-icon="el-icon-lock"
                  placeholder="密码"
                  show-password
                  class="custom-input"
                  @input="updatePasswordStrength"
                />
                <div class="password-strength">
                  <div
                    class="strength-bar"
                    :class="[strengthClass, { active: strengthLevel >= 1 }]"
                  />
                  <div
                    class="strength-bar"
                    :class="[strengthClass, { active: strengthLevel >= 2 }]"
                  />
                  <div
                    class="strength-bar"
                    :class="[strengthClass, { active: strengthLevel >= 3 }]"
                  />
                  <span class="strength-text">{{ strengthText }}</span>
                </div>
              </el-form-item>

              <el-form-item prop="confirmPassword">
                <el-input
                  v-model="form.confirmPassword"
                  prefix-icon="el-icon-lock"
                  placeholder="确认密码"
                  show-password
                  class="custom-input"
                />
              </el-form-item>

              <el-button
                type="primary"
                class="submit-btn gradient-btn"
                :loading="registerLoading"
                @click="handleRegister"
              >
                {{ registerLoading ? "注册中..." : "立即注册" }}
              </el-button>
            </el-form>
          </div>
          <!-- </transition> -->

          <!-- 第三方登录 -->
          <div v-if="isLogin" class="third-party-login">
            <div class="divider">
              <span class="divider-text">快速登录</span>
            </div>
            <div class="oauth-buttons">
              <el-tooltip content="微信登录" placement="top">
                <div class="oauth-btn wechat" @click="handleWechatLogin">
                  <i class="iconfont icon-wechat" />
                  <svg-icon icon-class="wechat" />
                </div>
              </el-tooltip>
              <el-tooltip content="GitHub登录" placement="top">
                <div class="oauth-btn github" @click="handleGithubLogin">
                  <i class="iconfont icon-github" />
                  <svg-icon icon-class="github" />
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>

      <!-- 找回密码流程 -->
      <div v-if="showForgotPassword" key="forgot" class="main-box">
        <el-steps :active="resetStep" align-center>
          <el-step title="验证邮箱" />
          <el-step title="输入验证码" />
          <el-step title="设置新密码" />
        </el-steps>

        <div class="reset-form">
          <!-- 第一步验证邮箱 -->
          <el-form
            v-if="resetStep === 1"
            ref="emailForm"
            :model="resetForm"
            :rules="resetRules"
          >
            <el-form-item prop="email">
              <el-input
                v-model="resetForm.email"
                placeholder="请输入注册邮箱"
                prefix-icon="el-icon-message"
                class="custom-input"
              />
            </el-form-item>
            <el-button
              type="primary"
              class="submit-btn"
              :loading="sending"
              @click="sendVerificationCode"
            >
              {{ sending ? `${countdown}秒后重发` : "发送验证码" }}
            </el-button>
          </el-form>
          <!-- 第二步：验证码验证 -->
          <el-form
            v-if="resetStep === 2"
            ref="codeForm"
            :model="resetForm"
            :rules="resetRules"
          >
            <el-form-item prop="verificationCode">
              <el-input
                v-model="resetForm.verificationCode"
                placeholder="请输入6位验证码"
                prefix-icon="el-icon-unlock"
                class="custom-input"
                maxlength="6"
              />
            </el-form-item>

            <el-button type="primary" class="submit-btn" @click="validateCode">
              验证验证码
            </el-button>
          </el-form>
          <!-- 第三步：设置新密码 -->
          <el-form
            v-if="resetStep === 3"
            ref="passwordForm"
            :model="resetForm"
            :rules="resetRules"
          >
            <el-form-item prop="newPassword">
              <el-input
                v-model="resetForm.newPassword"
                placeholder="请输入新密码"
                prefix-icon="el-icon-lock"
                class="custom-input"
                show-password
              />
            </el-form-item>

            <el-form-item prop="confirmNewPassword">
              <el-input
                v-model="resetForm.confirmNewPassword"
                placeholder="请确认新密码"
                prefix-icon="el-icon-lock"
                class="custom-input"
                show-password
              />
            </el-form-item>

            <el-button
              type="primary"
              class="submit-btn"
              @click="submitNewPassword"
            >
              确定修改
            </el-button>
          </el-form>

          <div class="back-login">
            <el-link type="info" @click="backToLogin">
              <i class="el-icon-arrow-left" /> 返回登录
            </el-link>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import gsap from "gsap"; // 引入GSAP动画库
import {
  register,
  sendEmailCode,
  validateEmailCode,
  updateAccountPassword,
} from "@/api/login";

export default {
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value !== this.resetForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    const validatePass = (rule, value, callback) => {
      if (value !== this.form.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };

    return {
      redirect: undefined,
      isWelcomeTitle: false,
      isLogin: true,
      loading: false,
      registerLoading: false,
      rememberMe: false,
      transitionName: "slide-right",
      strengthLevel: 0,
      strengthText: "密码强度",
      showForgotPassword: false,
      resetStep: 1,
      sending: false,
      countdown: 60,
      resetForm: {
        email: "",
        verificationCode: "",
        newPassword: "",
        confirmNewPassword: "",
      },
      loginData: {
        username: "",
        loginPassword: "",
      },
      form: {
        username: "",
        password: "",
        confirmPassword: "",
        email: "",
      },
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            min: 3,
            max: 12,
            message: "长度在 3 到 12 个字符",
            trigger: "blur",
          },
          {
            pattern: /^[A-Za-z0-9]+$/,
            message: "只能输入英文字母和数字",
            trigger: "blur",
          },
        ],
        email: [
          { required: true, message: "请输入邮箱地址", trigger: "blur" },
          { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
        ],
        loginPassword: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 16,
            message: "长度在 6 到 16 个字符",
            trigger: "blur",
          },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 16,
            message: "长度在 6 到 16 个字符",
            trigger: "blur",
          },
          { validator: this.validatePasswordStrength, trigger: "blur" },
        ],
        confirmPassword: [
          { required: true, message: "请再次输入密码", trigger: "blur" },
          { validator: validatePass, trigger: "blur" },
        ],
      },
      resetRules: {
        email: [
          { required: true, message: "请输入邮箱地址", trigger: "blur" },
          { type: "email", message: "邮箱格式不正确", trigger: "blur" },
        ],
        verificationCode: [
          { required: true, message: "请输入验证码", trigger: "blur" },
          { len: 6, message: "验证码为6位数字", trigger: "blur" },
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { min: 6, max: 16, message: "长度在6到16个字符", trigger: "blur" },
        ],
        confirmNewPassword: [
          { required: true, message: "请确认新密码", trigger: "blur" },
          { validator: validatePassword, trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    strengthClass() {
      return {
        0: "weak",
        1: "weak",
        2: "medium",
        3: "strong",
      }[this.strengthLevel];
    },
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  mounted() {
    const rememberInfo = localStorage.getItem("loginInfo");
    if (rememberInfo) {
      const { username, password } = JSON.parse(rememberInfo);
      this.loginData.username = username;
      this.loginData.loginPassword = password;
      this.rememberMe = true;
    }
  },
  methods: {
    // 在methods中添加
    // handleIconClick() {
    //   gsap.to(this.$el.querySelector(".title-icon"), {
    //     duration: 0.6,
    //     scale: 0.9,
    //     rotate: 360,
    //     ease: "power2.out",
    //   });
    // },

    handleIconClick() {
      this.isWelcomeTitle = !this.isWelcomeTitle;
      // 优化后的点击动画
      const timeline = gsap.timeline({
        defaults: {
          duration: 0.6,
          ease: "power2.out",
        },
      });

      timeline
        .to(
          ".title-underline",
          {
            scaleX: 1.2,
            backgroundColor: "#e74c3c",
          },
          "<30%"
        )
        .to(".title-icon", {
          scale: 0.9,
          rotate: 360,
          transformOrigin: "center",
        })
        .to(
          ".halo",
          {
            strokeWidth: 4,
            opacity: 1,
            duration: 0.3,
          },
          "<"
        )
        .to(
          ".title-text",
          {
            color: "#2980b9",
            duration: 0.4,
            onComplete: () => {
              // 动画完成后恢复颜色
              gsap.to(".title-text", {
                color: "inherit",
                duration: 0.3,
              });
            },
          },
          "-=0.2"
        );
    },
    enterTransition(el, done) {
      gsap.from(el, {
        duration: 0.6,
        autoAlpha: 0,
        scale: 0.98,
        x: this.showForgotPassword ? 80 : -80,
        ease: "power2.out",
        onComplete: done,
      });
    },
    leaveTransition(el, done) {
      gsap.to(el, {
        duration: 0.5,
        autoAlpha: 0,
        scale: 0.98,
        x: this.showForgotPassword ? -80 : 80,
        ease: "power2.inOut",
        onComplete: done,
      });
    },
    // 在返回登录方法中添加
    backToLogin() {
      this.showForgotPassword = false;
    },
    // 触发找回密码流程
    startForgotPassword() {
      this.showForgotPassword = true;
    },
    // 发送验证码
    async sendVerificationCode() {
      try {
        //这里不通过还是可以发送邮箱
        if (this.$refs.emailForm.validate()) {
          this.sending = true;
          // 启动倒计时
          const timer = setInterval(() => {
            if (this.countdown <= 0) {
              clearInterval(timer);
              this.sending = false;
              this.countdown = 60;
            } else {
              this.countdown--;
            }
          }, 1000);
          // 模拟API请求
          // await new Promise((resolve) => setTimeout(resolve, 1000));
          sendEmailCode({ email: this.resetForm.email }).then((res) => {
            if (res.code === 200) {
              this.resetStep = 2;
              this.$message.success("验证码已发送至邮箱");
            }
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    // 验证验证码
    async validateCode() {
      try {
        await this.$refs.codeForm.validate();

        // 模拟验证码验证
        // if (this.resetForm.verificationCode !== "123456") {
        //   // 测试用固定验证码
        //   throw new Error("验证码错误");
        // }
        const data = {
          email: this.resetForm.email,
          code: this.resetForm.verificationCode,
        };
        validateEmailCode(data).then((res) => {
          if (res.code === 200) {
            this.resetStep = 3;
            this.$message.success("验证码正确");
          }
        });
      } catch (error) {
        this.$message.error(error.message || "验证失败");
      }
    },
    // 提交新密码
    async submitNewPassword() {
      try {
        await this.$refs.passwordForm.validate();

        const data = {
          email: this.resetForm.email,
          password: this.resetForm.newPassword,
        };

        // 模拟API请求
        // await new Promise((resolve) => setTimeout(resolve, 1000));
        updateAccountPassword(data).then((res) => {
          if (res.code === 200) {
            this.$message.success("密码修改成功");
            this.showForgotPassword = false;
            this.resetForm = {
              email: "",
              verificationCode: "",
              newPassword: "",
              confirmNewPassword: "",
            };
          }
        });
      } catch (error) {
        this.$message.error("密码修改失败");
      }
    },
    switchForm(type) {
      if (type === "login") {
        this.transitionName = "slide-right";
      } else {
        this.transitionName = "slide-left";
      }
      this.isLogin = type === "login";
    },
    updatePasswordStrength() {
      const strongRegex =
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
      const mediumRegex = /^(?=.*[a-zA-Z])(?=.*\d)[A-Za-z\d]{6,}$/;

      if (strongRegex.test(this.form.password)) {
        this.strengthLevel = 3;
        this.strengthText = "强";
      } else if (mediumRegex.test(this.form.password)) {
        this.strengthLevel = 2;
        this.strengthText = "中";
      } else if (this.form.password.length > 0) {
        this.strengthLevel = 1;
        this.strengthText = "弱";
      } else {
        this.strengthLevel = 0;
        this.strengthText = "密码强度";
      }
    },
    validatePasswordStrength(rule, value, callback) {
      if (this.strengthLevel < 2) {
        callback(new Error("密码强度不足，请至少包含字母和数字"));
      } else {
        callback();
      }
    },
    async handleLogin() {
      try {
        this.loading = true;
        await this.$refs.loginForm.validate();

        await new Promise((resolve) => setTimeout(resolve, 1500));

        if (this.rememberMe) {
          localStorage.setItem(
            "loginInfo",
            JSON.stringify({
              username: this.loginData.username,
              password: this.loginData.loginPassword,
            })
          );
        } else {
          localStorage.removeItem("loginInfo");
        }
        this.$store.dispatch("Login", this.loginData).then(() => {
          this.$message.success("登录成功");
          this.$router.push({ path: this.redirect || "/" });
        });
      } catch (error) {
        console.error(error);
      } finally {
        this.loading = false;
      }
    },
    async handleRegister() {
      this.registerLoading = true;
      try {
        await this.$refs.registerForm.validate();
        const registerData = this.form;

        // await new Promise((resolve) => setTimeout(resolve, 1500));

        register(registerData).then((res) => {
          this.$message.success("注册成功");
          this.switchForm("login");
          this.resetRegisterForm();
          this.registerLoading = false;
        });
        // .catch((error) => {
        //   this.$message.error("注册失败");
        // });

        // this.$message.success("注册成功");
        // this.switchForm("login");
      } catch (error) {
        console.error(error);
      } finally {
        // this.registerLoading = false;
      }
    },
    resetRegisterForm() {
      this.form.username = "";
      this.form.password = "";
      this.form.confirmPassword = "";
      this.form.email = "";
      this.strengthLevel = 0;
    },
    handleWechatLogin() {
      this.$message.info("微信登录功能待实现");
    },
    handleGithubLogin() {
      this.$message.info("GitHub登录功能待实现");
    },
  },
};
</script>

<style scoped>
.title-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  vertical-align: middle;
  /* animation: iconRotate 1s ease; */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  cursor: pointer;
  transition: filter 0.3s;
}
.title-icon:active {
  filter: brightness(1.1);
}

.title-icon:hover {
  filter: drop-shadow(0 2px 6px rgba(52, 152, 219, 0.4));
}
/* 图标颜色 */
.icon-main {
  fill: none;
  stroke: #3498db;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}
/* 光环动画 */
.halo {
  stroke: #3498db;
  stroke-dasharray: 180;
  stroke-dashoffset: 180;
  opacity: 0.6;
  animation: haloRotate 8s linear infinite;
}
@keyframes haloRotate {
  0% {
    stroke-dashoffset: 180;
    transform: rotate(0deg);
  }
  50% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -180;
    transform: rotate(360deg);
  }
}
/* 图标入场动画 */
.system-title .title-icon {
  animation: iconEntrance 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55),
    iconFloat 3s ease-in-out infinite;
}
@keyframes iconEntrance {
  from {
    opacity: 0;
    transform: translateX(-20px) rotate(-180deg) scale(0.5);
  }
  to {
    opacity: 1;
    transform: translateX(0) rotate(0) scale(1);
  }
}
@keyframes iconFloat {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}
@keyframes iconRotate {
  from {
    transform: rotate(-180deg);
  }
  to {
    transform: rotate(0);
  }
}
/* 系统标题样式 */
.system-title {
  position: absolute;
  top: 20px;
  width: 100%;
  text-align: center;
  margin: 0;
  padding: 0;
  z-index: 10;
}

.title-text {
  transition: color 0.3s ease;
  min-width: 200px; /* 保持标题区域宽度 */
  will-change: transform, opacity; /* 创建独立渲染层 */
  backface-visibility: hidden; /* 避免字体模糊 */
  display: inline-block;
  font-size: 2.8rem;
  font-weight: 700;
  letter-spacing: 2px;
  background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-10px);
  opacity: 0;
  animation: titleEnter 0.8s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}

.title-underline {
  width: 120px;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #3498db 50%,
    transparent 100%
  );
  margin: 8px auto;
  opacity: 0;
  animation: underlineScale 0.6s 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

@keyframes titleEnter {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes underlineScale {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 0.8;
  }
}
.login-container {
  padding-top: 100px; /* 增加顶部间距 */
  box-sizing: border-box;
  position: relative;
  min-height: 100vh;
  /* height: 100vh; */
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.main-box {
  /* transition: transform 0.3s, opacity 0.3s; */
  /* position: relative; */
  position: absolute;
  /* width: 420px; */
  width: 400px;
  min-height: 480px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 20px;
  /* box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2); */
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
  /* backdrop-filter: blur(10px); */
  backface-visibility: hidden;
  will-change: transform, opacity;
}

.switch-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.switch-item {
  position: relative;
  padding: 0 25px;
  font-size: 18px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.switch-item.active {
  color: #409eff;
  font-weight: 500;
}

.switch-item:hover {
  color: #409eff;
}

.underline {
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
  height: 2px;
  background: #409eff;
  transform: scaleX(0);
  transition: transform 0.3s;
}

.switch-item.active .underline {
  transform: scaleX(1);
}

.custom-input {
  transition: all 0.3s;
}

.custom-input:hover >>> .el-input__inner {
  border-color: #409eff;
}

.custom-input >>> .el-input__inner {
  height: 46px;
  line-height: 46px;
  border-radius: 8px;
  padding-left: 40px;
  transition: all 0.3s;
}

.custom-input >>> .el-input__prefix {
  left: 10px;
  transition: color 0.3s;
}

.custom-input:hover >>> .el-input__prefix {
  color: #409eff;
}

.remember-me {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
}

.custom-checkbox >>> .el-checkbox__label {
  color: #606266;
}

.forgot-password {
  /* font-size: 13px; */
  margin-top: 20px;
  display: block;
  text-align: center;
}

.gradient-btn {
  background: linear-gradient(45deg, #409eff, #67c23a);
  border: none;
  border-radius: 8px;
  height: 46px;
  font-size: 16px;
  letter-spacing: 1px;
  transition: all 0.3s;
}

.gradient-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.3);
}

.password-strength {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #ebeef5;
  border-radius: 2px;
  transition: all 0.3s;
}

.strength-bar.active {
  &.weak {
    background: #ff4d4f;
  }
  &.medium {
    background: #faad14;
  }
  &.strong {
    background: #52c41a;
  }
}

.strength-text {
  font-size: 12px;
  color: #909399;
}

.third-party-login {
  margin-top: 30px;
}

.divider {
  position: relative;
  margin: 25px 0;
  text-align: center;
}

.divider-text {
  display: inline-block;
  padding: 0 10px;
  background: white;
  color: #909399;
  font-size: 12px;
  position: relative;
  z-index: 1;
}

.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #dcdfe6;
  z-index: 0;
}

.oauth-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.oauth-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.oauth-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.wechat {
  background: #07c160;
  color: white;
}

.github {
  background: #333;
  color: white;
}

.iconfont {
  font-size: 24px;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  /* transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1); */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  width: 100%;
}
.slide-left-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

@media (max-width: 480px) {
  .title-text {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .title-underline {
    width: 80px;
    height: 2px;
  }
  .login-container {
    padding-top: 80px;
  }
  .main-box {
    /* width: 90%;
    padding: 25px; */
    width: 88%;
    min-height: 85vh;
    padding: 30px;
  }
}
.reset-form {
  margin-top: 30px;
}

.el-steps {
  margin-bottom: 30px;
}

.back-login {
  text-align: center;
  /* margin-top: 20px; */
  margin-top: 30px;
}

/* 新增倒计时按钮样式 */
.submit-btn {
  width: 100%;
  margin-top: 20px;
  letter-spacing: 1px;
}
</style>
