import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import './assets/icons';
import VueParticles from 'vue-particles';
import './permisson';
import './assets/styles/element-variables.scss';
import '@/assets/styles/index.scss';
import '@/assets/styles/ruoyi.scss';
// import VueMeta from 'vue-meta';

// 全局设置 Element UI 的 Dialog 组件默认不通过点击遮罩关闭
import { Dialog } from 'element-ui';
Dialog.props.closeOnClickModal.default = false;

Vue.use(ElementUI);
Vue.use(VueParticles);
// Vue.use(VueMeta);
Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount('#app');
