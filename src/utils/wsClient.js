// import WebSocket from "ws";
const NODE_ENV = process.env.NODE_ENV;

let ws = null;
let maxReConnectTimes = null;
let lockReconnect = false;
let wsUrl = null;
// let sender = null;
let needReconnect = null;

const listeners = [];

/**
 * 注册监听函数
 * @param {Function} cb
 */
const onMessage = (cb) => {
  if (typeof cb === 'function') {
    listeners.push(cb);
  }
};
//通知所有监听者
const notifyListeners = (data) => {
  listeners.forEach((cb) => cb(data));
};

const initWs = (token) => {
  //目前先写死用开发环境的ws连接
  wsUrl = `${
    NODE_ENV !== 'development' ? 'ws://localhost:9999/ws' : 'ws://localhost:9999/ws'
  }?token=${token}`;
  // console.log(wsUrl);
  // sender = _sender;
  needReconnect = true;
  maxReConnectTimes = 5;
  if (window.WebSocket) {
    console.log('您的浏览器支持websocket,正在尝试连接');
    createWs();
  } else {
    console.log('您的浏览器不支持websocket');
  }
};

const closeWs = () => {
  needReconnect = false;
  ws.close();
};

const createWs = () => {
  if (wsUrl === null) {
    return;
  }
  ws = new WebSocket(wsUrl);
  ws.onopen = function () {
    console.log('客户段连接成功');
    ws.send('heart beat');
    maxReConnectTimes = 5;
  };
  //从服务器接受到信息的回调函数
  ws.onmessage = function (e) {
    console.log('收到服务器消息', e.data);
    notifyListeners(e.data); //通知所有监听者
  };
  ws.onclose = function () {
    console.log('关闭客户端连接重连');
    reconnect();
  };
  ws.onerror = function () {
    console.log('连接失败了准备重连');
    reconnect();
  };
  const reconnect = () => {
    if (!needReconnect) {
      console.log('连接断开无需重连');
      return;
    }
    if (ws !== null) {
      ws.close();
    }
    if (lockReconnect) {
      return;
    }
    lockReconnect = true;
    if (maxReConnectTimes > 0) {
      console.log('准备重连，剩余重连次数' + maxReConnectTimes, new Date().getTime);
      maxReConnectTimes--;
      setTimeout(() => {
        createWs();
        lockReconnect = false;
      }, 5000);
    } else {
      console.log('连接已经超时');
    }
  };
  setInterval(() => {
    if (ws !== null && ws.readyState === 1) {
      ws.send('heart beat');
    }
  }, 5000);
};

export { initWs, onMessage };
