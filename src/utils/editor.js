// 防抖函数
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle;
  return function (...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// 字数统计
export function countWords(text) {
  // 中文字符
  const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
  // 英文单词
  const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
  // 数字
  const numbers = (text.match(/\d+/g) || []).length;

  return {
    total: chineseChars + englishWords + numbers,
    chinese: chineseChars,
    english: englishWords,
    numbers: numbers,
  };
}

// 阅读时间估算（按平均阅读速度）
export function estimateReadingTime(wordCount) {
  const wordsPerMinute = 200; // 平均阅读速度
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  return minutes;
}

// 生成随机ID
export function generateId() {
  return Math.random().toString(36).substr(2, 9);
}

// 格式化文件大小
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 检查文件类型
export function checkFileType(file, allowedTypes) {
  return allowedTypes.some((type) => {
    if (type.endsWith('/*')) {
      return file.type.startsWith(type.slice(0, -1));
    }
    return file.type === type;
  });
}

// 压缩图片
export function compressImage(file, maxWidth = 1920, maxHeight = 1080, quality = 0.8) {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = function () {
      // 计算压缩后的尺寸
      let { width, height } = img;

      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, width, height);

      // 转换为 Blob
      canvas.toBlob(resolve, 'image/jpeg', quality);
    };

    img.src = URL.createObjectURL(file);
  });
}

// 文本相似度检查（简化版）
export function checkSimilarity(text1, text2) {
  const words1 = text1.toLowerCase().split(/\s+/);
  const words2 = text2.toLowerCase().split(/\s+/);

  const set1 = new Set(words1);
  const set2 = new Set(words2);

  const intersection = new Set([...set1].filter((x) => set2.has(x)));
  const union = new Set([...set1, ...set2]);

  return intersection.size / union.size;
}

// SEO 分析
export function analyzeSEO(title, content, excerpt) {
  const issues = [];
  const suggestions = [];

  // 标题分析
  if (!title) {
    issues.push('缺少标题');
  } else if (title.length < 10) {
    issues.push('标题过短，建议10-60个字符');
  } else if (title.length > 60) {
    issues.push('标题过长，可能在搜索结果中被截断');
  }

  // 内容分析
  if (!content) {
    issues.push('缺少内容');
  } else if (content.length < 300) {
    issues.push('内容过短，建议至少300个字符');
  }

  // 摘要分析
  if (!excerpt) {
    suggestions.push('建议添加文章摘要');
  } else if (excerpt.length < 50) {
    suggestions.push('摘要过短，建议50-160个字符');
  } else if (excerpt.length > 160) {
    suggestions.push('摘要过长，可能在搜索结果中被截断');
  }

  // 标题在内容中的出现
  if (title && content && !content.toLowerCase().includes(title.toLowerCase())) {
    suggestions.push('建议在正文中提及标题关键词');
  }

  return {
    score: Math.max(0, 100 - issues.length * 20 - suggestions.length * 10),
    issues,
    suggestions,
  };
}
