/*
优化后的 axios 配置文件
主要改进：
1. 请求拦截器中优化防重复提交逻辑；
2. 响应拦截器中针对状态码的错误处理进行了补充；
3. 下载方法使用 try…finally 确保 Loading 被关闭；
*/
import axios from 'axios';
import { Notification, MessageBox, Message, Loading } from 'element-ui';
import store from '@/store';
import { getAccessToken } from '@/utils/auth';
import errorCode from '@/utils/errorCode';
import { tansParams, blobValidate } from '@/utils/common';
import cache from '@/plugins/cache';
import { saveAs } from 'file-saver';

// 下载时 Loading 对象，引入全局变量保存 Loading 实例
let downloadLoadingInstance;
// 是否显示重新登录（用于防止重复弹出）
export let isRelogin = { show: false };
// 默认设置请求 Content-Type
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
// 创建 axios 实例
const service = axios.create({
  // baseURL 可在环境变量中配置
  baseURL: process.env.VUE_APP_BASE_API,
  // 请求超时时间 (10 秒)
  timeout: 10000,
});

/**
请求拦截器
*/
service.interceptors.request.use(
  (config) => {
    // 判断是否需要设置 token
    const isToken = (config.headers || {}).isToken === false;
    if (getAccessToken() && !isToken) {
      config.headers['Authorization'] = getAccessToken(); // 每个请求携带 token
    }
    // 针对 GET 请求做参数转换，将 params 转化为 queryString
    if (config.method === 'get' && config.params) {
      let url = config.url + '?';
      for (const propName of Object.keys(config.params)) {
        const value = config.params[propName];
        const part = encodeURIComponent(propName) + '=';
        if (value !== null && typeof value !== 'undefined') {
          if (typeof value === 'object') {
            for (const key of Object.keys(value)) {
              let params = propName + '[' + key + ']';
              const subPart = encodeURIComponent(params) + '=';
              url += subPart + encodeURIComponent(value[key]) + '&';
            }
          } else {
            url += part + encodeURIComponent(value) + '&';
          }
        }
      }
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }
    // 防止数据重复提交
    // 只有 post/put 请求且未设置禁止重复提交标记时才进行重复提交校验
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      // 封装请求数据对象，转成 JSON 格式字符串便于比较
      const requestObj = {
        url: config.url,
        // 若数据为对象需序列化
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime(),
      };
      // 直接根据 JSON 字符串的长度判断数据大小（单位为字符数）
      const requestSize = JSON.stringify(requestObj).length;
      // 限制 5MB 的数据量（对于超大数据不做重复提交校验）
      const limitSize = 5 * 1024 * 1024;
      if (requestSize >= limitSize) {
        console.warn(
          `[${config.url}]: 请求数据大小(${requestSize})超出允许的5M限制，无法进行防重复提交验证。`
        );
        return config;
      }
      // 从 session 缓存中取出之前的请求对象
      const sessionObj = cache.session.getJSON('sessionObj');
      if (!sessionObj) {
        cache.session.setJSON('sessionObj', requestObj);
      } else {
        const { url: s_url, data: s_data, time: s_time } = sessionObj;
        const interval = 1000; // 设置时间间隔 1 秒，低于此间隔视为重复提交
        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          const message = '数据正在处理，请勿重复提交';
          console.warn(`[${s_url}]: ` + message);
          // 使用 reject 阻止此次请求发出
          return Promise.reject(new Error(message));
        } else {
          cache.session.setJSON('sessionObj', requestObj);
        }
      }
    }
    return config;
  },
  (error) => {
    console.error('请求拦截器错误：', error);
    return Promise.reject(error);
  }
);

/**
响应拦截器
*/
service.interceptors.response.use(
  (res) => {
    // 默认状态码为 200
    const code = res.data.code || 200;
    // 根据状态码或后端返回信息获取提示信息
    const msg = res.data.message || errorCode[code] || errorCode['default'];
    // 二进制数据直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res.data;
    }
    // 401: 登录状态已过期，需要重新登录
    if (code === 401) {
      if (!isRelogin.show) {
        isRelogin.show = true;
        MessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            isRelogin.show = false;
            store.dispatch('LogOut').then(() => {
              location.href = '/index';
            });
          })
          .catch(() => {
            isRelogin.show = false;
          });
      }
      return Promise.reject(new Error('无效的会话，或者会话已过期，请重新登录。'));
    } else if (code === 500) {
      Message({ message: msg, type: 'error' });
      return Promise.reject(new Error(msg));
    } else if (code === 501) {
      Message({
        type: 'error',
        duration: 0,
        message: msg,
      });
      return Promise.reject(new Error(msg));
    } else if (code !== 200) {
      // 其他错误统一使用 Notification 弹出错误提示
      Notification.error({ title: msg });
      return Promise.reject('error');
    } else {
      return res.data;
    }
  },
  (error) => {
    console.error('响应拦截器错误：', error);
    let { message } = error;
    if (message === 'Network Error') {
      message = '后端接口连接异常';
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时';
    } else if (message.includes('Request failed with status code')) {
      // 截取状态码最后 3 位构造错误提示
      message = '系统接口' + message.substr(message.length - 3) + '异常';
    }
    Message({ message: message, type: 'error', duration: 5 * 1000 });
    return Promise.reject(error);
  }
);

/**
通用下载方法（使用 async/await + try…finally 结构保证 Loading 一定关闭）
参数：
url: 后台接口地址
params: 请求参数
filename: 下载保存的文件名
config: 可选的其他 axios 配置（例如 headers 改写等）
*/
export async function download(url, params, filename, config = {}) {
  downloadLoadingInstance = Loading.service({
    text: '正在下载数据，请稍候',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
  });
  try {
    // 使用 post 请求发起文件下载，此处转换请求参数，使用 form 表单的提交方式
    const data = await service.post(url, params, {
      transformRequest: [(params) => tansParams(params)],
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob',
      ...config,
    });
    // 通过 blobValidate 判断返回数据是否为有效的 blob 文件
    const isBlob = blobValidate(data);
    if (isBlob) {
      const blob = new Blob([data]);
      saveAs(blob, filename);
    } else {
      // 如果返回的不是一个 blob 文件，则按文本方式解析返回消息
      const resText = await data.text();
      const rspObj = JSON.parse(resText);
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default'];
      Message.error(errMsg);
    }
  } catch (error) {
    console.error('下载文件出错：', error);
    Message.error('下载文件出现错误，请联系管理员！');
  } finally {
    // 确保无论成功与否都关闭 Loading 动画
    downloadLoadingInstance.close();
  }
}
export default service;
