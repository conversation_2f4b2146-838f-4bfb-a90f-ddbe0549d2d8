// Markdown 转 HTML 的简化实现
export function markdownToHtml(markdown) {
  return (
    markdown
      // 标题
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')

      // 粗体
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/__(.*?)__/gim, '<strong>$1</strong>')

      // 斜体
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      .replace(/_(.*?)_/gim, '<em>$1</em>')

      // 删除线
      .replace(/~~(.*?)~~/gim, '<del>$1</del>')

      // 行内代码
      .replace(/`(.*?)`/gim, '<code>$1</code>')

      // 代码块
      .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')

      // 链接
      .replace(/$$([^$$]+)\]$ ([^)]+) $ /gim, '<a href="$2" target="_blank">$1</a>')

      // 图片
      .replace(/!$$([^$$]*)\]$ ([^)]+) $ /gim, '<img src="$2" alt="$1" style="max-width: 100%;" />')

      // 无序列表
      .replace(/^\* (.+)$/gim, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')

      // 有序列表
      .replace(/^\d+\. (.+)$/gim, '<li>$1</li>')

      // 引用
      .replace(/^> (.+)$/gim, '<blockquote>$1</blockquote>')

      // 分割线
      .replace(/^---$/gim, '<hr>')

      // 段落
      .replace(/\n\n/gim, '</p><p>')
      .replace(/^(?!<[hul]|<blockquote|<hr|<pre)/gim, '<p>')
      .replace(/(?![hul]>|\/blockquote>|hr>|\/pre>)$/gim, '</p>')
  );
}

// HTML 转 Markdown 的简化实现
export function htmlToMarkdown(html) {
  return (
    html
      // 标题
      .replace(/<h1>(.*?)<\/h1>/gim, '# $1\n\n')
      .replace(/<h2>(.*?)<\/h2>/gim, '## $1\n\n')
      .replace(/<h3>(.*?)<\/h3>/gim, '### $1\n\n')
      .replace(/<h4>(.*?)<\/h4>/gim, '#### $1\n\n')
      .replace(/<h5>(.*?)<\/h5>/gim, '##### $1\n\n')
      .replace(/<h6>(.*?)<\/h6>/gim, '###### $1\n\n')

      // 粗体和斜体
      .replace(/<strong>(.*?)<\/strong>/gim, '**$1**')
      .replace(/<b>(.*?)<\/b>/gim, '**$1**')
      .replace(/<em>(.*?)<\/em>/gim, '*$1*')
      .replace(/<i>(.*?)<\/i>/gim, '*$1*')

      // 删除线
      .replace(/<del>(.*?)<\/del>/gim, '~~$1~~')
      .replace(/<s>(.*?)<\/s>/gim, '~~$1~~')

      // 代码
      .replace(/<code>(.*?)<\/code>/gim, '`$1`')
      .replace(/<pre><code>([\s\S]*?)<\/code><\/pre>/gim, '```\n$1\n```')

      // 链接
      .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gim, '[$2]($1)')

      // 图片
      .replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gim, '![$2]($1)')
      .replace(/<img[^>]*alt="([^"]*)"[^>]*src="([^"]*)"[^>]*>/gim, '![$1]($2)')
      .replace(/<img[^>]*src="([^"]*)"[^>]*>/gim, '![]($1)')

      // 列表
      .replace(/<ul><li>(.*?)<\/li><\/ul>/gims, (match, content) => {
        return (
          content
            .split('</li><li>')
            .map((item) => `* ${item}`)
            .join('\n') + '\n'
        );
      })
      .replace(/<ol><li>(.*?)<\/li><\/ol>/gims, (match, content) => {
        return (
          content
            .split('</li><li>')
            .map((item, index) => `${index + 1}. ${item}`)
            .join('\n') + '\n'
        );
      })

      // 引用
      .replace(/<blockquote>(.*?)<\/blockquote>/gim, '> $1\n')

      // 分割线
      .replace(/<hr[^>]*>/gim, '---\n')

      // 段落和换行
      .replace(/<p>(.*?)<\/p>/gim, '$1\n\n')
      .replace(/<br[^>]*>/gim, '\n')

      // 移除剩余HTML标签
      .replace(/<[^>]+>/g, '')

      // 清理多余空行
      .replace(/\n{3,}/g, '\n\n')
      .trim()
  );
}

// 提取文本内容（用于字数统计）
export function extractText(markdown) {
  return markdown
    .replace(/```[\s\S]*?```/g, '') // 移除代码块
    .replace(/`[^`]+`/g, '') // 移除行内代码
    .replace(/!$$[^$$]*\]$ [^)]* $ /g, '') // 移除图片
    .replace(/$$[^$$]+\]$ [^)]+ $ /g, '') // 移除链接（保留文本）
    .replace(/[#*>`~_\-+=[\]()]/g, '') // 移除Markdown标记
    .replace(/\s+/g, ' ') // 合并空白字符
    .trim();
}

// 生成目录
export function generateTOC(markdown) {
  const headings = [];
  const lines = markdown.split('\n');

  lines.forEach((line, index) => {
    const match = line.match(/^(#{1,6})\s+(.+)$/);
    if (match) {
      const level = match[1].length;
      const title = match[2].trim();
      const id = title
        .toLowerCase()
        .replace(/[^\w\u4e00-\u9fa5]+/g, '-')
        .replace(/^-+|-+$/g, '');

      headings.push({
        level,
        title,
        id,
        line: index + 1,
      });
    }
  });

  return headings;
}
