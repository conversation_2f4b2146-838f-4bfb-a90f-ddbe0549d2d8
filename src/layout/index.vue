<!-- <template>
  <div
    :class="classObj"
    class="app-wrapper"
    :style="{ '--current-color': theme }"
  >
    <sidebar v-if="!sidebar.hide" class="sidebar-container" />
    <div
      :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }"
      class="main-container"
    >
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
    </div>
  </div>
</template>

<script>
import { Navbar, Sidebar, AppMain, TagsView } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";

export default {
  name: "Layout",
  components: {
    Navbar,
    Sidebar,
    AppMain,
    TagsView,
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
    variables() {
      return variables;
    },
  },
  data() {
    return {
      hasScroll: false,
    };
  },
  mounted() {
    // 监听滚动事件，添加阴影效果
    window.addEventListener("scroll", this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    handleScroll() {
      this.hasScroll = window.pageYOffset > 0;
      const header = document.querySelector(".fixed-header");
      if (header) {
        if (this.hasScroll) {
          header.classList.add("has-shadow");
        } else {
          header.classList.remove("has-shadow");
        }
      }
    },
  },
};
</script> -->
<!-- <script>
import { Sidebar, AppMain, Navbar, TagsView } from "./components";
import { mapState } from "vuex";

export default {
  name: "Layout",
  components: {
    Sidebar,
    AppMain,
    Navbar,
    TagsView,
  },
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
      theme: (state) => state.app.theme,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
      };
    },
  },
};
</script> -->

<!-- <style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;

  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

.sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 220px;
  height: 100%;
  background-color: #304156;
  transition: width 0.28s;
  z-index: 1001;
  overflow: hidden;
}

.main-container {
  min-height: 100%;
  transition: margin-left 0.28s;
  margin-left: 220px;
  position: relative;
}

.hideSidebar {
  .sidebar-container {
    width: 54px !important;
  }

  .main-container {
    margin-left: 54px;
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .app-wrapper {
    position: relative;
  }

  .sidebar-container {
    transition: transform 0.28s;
    width: 210px !important;
  }

  .hideSidebar {
    .sidebar-container {
      pointer-events: none;
      transition-duration: 0.3s;
      transform: translate3d(-210px, 0, 0);
    }
  }
}

.withoutAnimation {
  .main-container,
  .sidebar-container {
    transition: none;
  }
}
</style> -->
<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <sidebar v-if="!sidebar.hide" class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }" class="header-container">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
    </div>
  </div>
</template>
<script>
import { Navbar, Sidebar, AppMain, TagsView } from './components';
import ResizeMixin from './mixin/ResizeHandler';
import { mapState } from 'vuex';
import variables from '@/assets/styles/variables.scss';

export default {
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain,
    TagsView,
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile',
      };
    },
    variables() {
      return variables;
    },
  },
  data() {
    return {
      hasScroll: false,
    };
  },
  mounted() {
    // 监听滚动事件，添加阴影效果
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleScroll() {
      this.hasScroll = window.pageYOffset > 0;
      const header = document.querySelector('.fixed-header');
      if (header) {
        if (this.hasScroll) {
          header.classList.add('has-shadow');
        } else {
          header.classList.remove('has-shadow');
        }
      }
    },
  },
};
</script>
<!-- <script>
import { Navbar, Sidebar, AppMain, TagsView } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";

export default {
  name: "Layout",
  components: {
    Navbar,
    Sidebar,
    AppMain,
    TagsView,
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
    variables() {
      return variables;
    },
  },
  data() {
    return {
      hasScroll: false,
    };
  },
  mounted() {
    // 监听滚动事件，添加阴影效果
    window.addEventListener("scroll", this.handleScroll);

    // 检查是否是首页，如果是则禁止滚动
    this.checkIsHomePage();
    this.$watch("$route.path", this.checkIsHomePage);
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    handleScroll() {
      this.hasScroll = window.pageYOffset > 0;
      const header = document.querySelector(".fixed-header");
      if (header) {
        if (this.hasScroll) {
          header.classList.add("has-shadow");
        } else {
          header.classList.remove("has-shadow");
        }
      }
    },
    checkIsHomePage() {
      if (this.$route.path === "/" || this.$route.path === "/index") {
        // 是首页，禁止滚动
        document.body.style.overflow = "hidden";
        document.documentElement.style.overflow = "hidden";
      } else {
        // 不是首页，恢复默认
        document.body.style.overflow = "";
        document.documentElement.style.overflow = "";
      }
    },
  },
}; -->
<!-- </script> -->

<style lang="scss" scoped>
@import '~@/assets/styles/mixin.scss';
@import '~@/assets/styles/variables.scss';

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 999;
  }
}

.main-container {
  min-height: 100%;
  height: 100%;
  transition: margin-left 0.28s;
  margin-left: $base-sidebar-width;
  position: relative;
  display: flex;
  flex-direction: column;
}

.header-container {
  width: 100%;
  z-index: 9;
  transition:
    width 0.28s,
    background-color 0.3s;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  width: calc(100% - #{$base-sidebar-width});
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}

.has-shadow {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
}
.sidebarHide .fixed-header {
  width: 100%;
}
</style>
