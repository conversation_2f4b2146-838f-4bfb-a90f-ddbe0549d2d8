import router from "./router";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getAccessToken } from "@/utils/auth";
import { isRelogin } from "@/utils/request";
// 配置 NProgress，不显示右上角加载的 spinner
NProgress.configure({ showSpinner: false });
// 使用 Set 来存放免登录的路径，提升查找效率
const whiteList = new Set(["/login", "/register", "/account/active"]);
// 使用 async/await 改进路由前置守卫逻辑
router.beforeEach(async (to, from, next) => {
  // 开启页面加载进度条
  NProgress.start();
  try {
    if (getAccessToken()) {
      // 用户有 token，优先设置页面 title
      // if (to.meta.title) {
      //   // 设置页面标题，可触发全局状态更新
      //   store.dispatch("settings/setTitle", to.meta.title);
      // }
      // 若已登录却要访问登录页面，自动跳转到首页
      if (to.path === "/login") {
        next({ path: "/" });
        return; // 注意：此处提前 return，后续代码不会再执行
      }
      // 若用户信息或权限还未加载，则动态获取用户信息和路由
      if (!store.getters.roles || store.getters.roles.length === 0) {
        // 显示 loading 状态或其他 UI 提示
        isRelogin.show = true;
        // 动态获取用户信息，建议里面检测 token 是否过期
        await store.dispatch("GetInfo");
        // 获取信息成功后，重置 loading 状态
        isRelogin.show = false;
        // 根据用户权限生成可访问路由表（通常后端返回权限标识，再过滤前端路由）
        const accessRoutes = await store.dispatch("GenerateRoutes");
        // 动态添加可访问路由表
        // router.addRoutes(accessRoutes);
        for (const route of accessRoutes) {
          router.addRoute(route);
        }
        // 确保 addRoutes 完成后，再以 replace 模式进入当前路由，避免重复历史记录
        next({ ...to, replace: true });
      } else {
        // 如果已经有用户信息，直接放行
        next();
      }
    } else {
      // 用户没有 token
      if (whiteList.has(to.path)) {
        // 白名单中的路径，可以直接进入
        next();
      } else {
        // 否则重定向到登录页，同时附带当前跳转的目标路径作为 redirect 参数，
        // 登录后可跳转回来
        next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
      }
    }
  } catch (error) {
    // 当出现错误时（例如获取用户信息失败）
    // 可尝试清除 token（建议 LogOut 中清除 token 和相关缓存）
    await store.dispatch("LogOut");
    // 使用 ElementUI 的 Message 弹出错误提示
    Message.error(error.message || "发生错误，请重新登录");
    // 跳转到登录页，并附带当前路径信息
    next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
  } finally {
    // 不管成功或失败，均结束进度条
    NProgress.done();
  }
});
// 全局后置钩子：确保路由切换完成后结束进度条
router.afterEach(() => {
  NProgress.done();
});
