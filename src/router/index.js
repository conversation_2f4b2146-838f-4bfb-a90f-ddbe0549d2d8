/*
文件说明：
定义了常驻路由 (constantRoutes) 与需要权限控制的动态路由 (dynamicRoutes)。
router.push 和 router.replace 方法增加了错误捕获，防止多次点击同一路由报错。
使用 history 模式移除 URL 中的“#”，并设置路由切换时页面滚动到顶部。
*/
import Vue from 'vue';
import Router from 'vue-router';

// 激活 router 插件
Vue.use(Router);

// Layout 布局组件，许多路由均基于 Layout 来展现
import Layout from '@/layout';
// import { component } from "vue/types/umd";

/*
常驻路由，无论用户权限如何，这些路由始终可用。
hidden 用于控制该路由是否在侧边栏中显示。
*/
export const constantRoutes = [
  // 重定向路由：用于处理页面跳转重定向逻辑
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        // 使用正则捕获路径（捕获所有路径）
        path: '/redirect/(.)',
        component: () => import('@/views/redirect'),
      },
    ],
  },
  // 登录页
  {
    path: '/login',
    component: () => import('@/views/Login'),
    hidden: true,
  },
  // 404 错误页
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  // 401 错误页
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  //激活页面
  {
    path: '/account/active',
    component: () => import('@/views/System/AccountActive'),
    hidden: true,
  },
  // 首页：根路径重定向至 index 页面
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: {
          title: '首页',
          icon: 'dashboard',
          affix: true,
          noScroll: true,
          noPadding: true,
        },
      },
    ],
  },
  {
    path: '/chat',
    component: Layout,
    redirect: 'wechat',
    children: [
      {
        path: 'wechat',
        component: () => import('@/views/chat/index'),
        name: 'wechat',
        meta: { title: '聊天', icon: 'chat', noScroll: true, noPadding: true },
      },
    ],
  },
  {
    path: '/blog',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        name: 'Blog',
        component: () => import('@/views/blog/index'),
        meta: { title: '写文章', icon: 'blog', noScroll: true, noPadding: true },
      },
    ],
  },
  {
    path: '/file',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        name: 'File',
        component: () => import('@/views/file/index'),
        meta: { title: '文件管理', icon: 'file', noScroll: true, noPadding: true },
      },
    ],
  },
  {
    path: '/password',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        name: 'password',
        component: () => import('@/views/password/index'),
        meta: { title: '密码管理', icon: 'password', noScroll: true, noPadding: true },
      },
    ],
  },
  // 个人中心页（隐藏在侧边栏中）
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/System/User/Profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' },
      },
    ],
  },
];

// 这些是根据权限添加的 动态路由，基于用户权限动态去加载
// export const dynamicRoutes = [
//   {
//     path: "/system/user-auth",
//     component: Layout,
//     hidden: true,
//     permissions: ["system:user:edit"],
//     children: [
//       {
//         path: "role/:userId(\\d+)",
//         component: () => import("@/views/system/user/authRole"),
//         name: "AuthRole",
//         meta: { title: "分配角色", activeMenu: "/system/user" },
//       },
//     ],
//   },
//   {
//     path: "/blog/wite",
//     component: Layout,
//     hidden: true,
//     permissions: ["blog:blog:add"],
//     children: [
//       {
//         path: "role/:userId(\\w+)",
//         component: () => import("@/views/blog/write/index"),
//         name: "WriteBlog",
//         meta: { title: "新增博客", activeMenu: "/blog/write" },
//       },
//     ],
//   },
//   {
//     path: "/system/role-auth",
//     component: Layout,
//     hidden: true,
//     permissions: ["system:role:edit"],
//     children: [
//       {
//         path: "user/:roleId(\\d+)",
//         component: () => import("@/views/system/role/authUser"),
//         name: "AuthUser",
//         meta: { title: "分配用户", activeMenu: "/system/role" },
//       },
//     ],
//   },
//   {
//     path: "/system/dict-data",
//     component: Layout,
//     hidden: true,
//     permissions: ["system:dict:list"],
//     children: [
//       {
//         path: "index/:dictId(\\w+)",
//         component: () => import("@/views/system/dict/data"),
//         name: "Data",
//         meta: { title: "字典数据", activeMenu: "/system/dict" },
//       },
//     ],
//   },
//   {
//     path: "/monitor/job-log",
//     component: Layout,
//     hidden: true,
//     permissions: ["monitor:job:list"],
//     children: [
//       {
//         path: "index/:jobId(\\w+)",
//         component: () => import("@/views/monitor/job/log"),
//         name: "JobLog",
//         meta: { title: "调度日志", activeMenu: "/monitor/job" },
//       },
//     ],
//   },
//   {
//     path: "/tool/gen-edit",
//     component: Layout,
//     hidden: true,
//     permissions: ["tool:gen:edit"],
//     children: [
//       {
//         path: "index/:tableId(\\d+)",
//         component: () => import("@/views/tool/gen/editTable"),
//         name: "GenEdit",
//         meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
//       },
//     ],
//   },
// ];

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((err) => err);
};
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch((err) => err);
};

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
// 防止连续点击多次路由报错
