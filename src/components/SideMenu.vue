<!-- <template>
  <el-menu
    :default-active="$route.path"
    router
    background-color="#304156"
    text-color="#bfcbd9"
    active-text-color="#409EFF"
  >
    <el-menu-item index="/">
      <i class="el-icon-s-home" />
      <span>首页</span>
    </el-menu-item>
    <template v-for="menu in menus">
      <el-submenu v-if="menu.children" :key="menu.path" :index="menu.path">
        <template slot="title">
          <i :class="menu.icon" />
          <span>{{ menu.name }}</span>
        </template>
        <el-menu-item
          v-for="child in menu.children"
          :key="child.path"
          :index="child.path"
        >
          {{ child.name }}
        </el-menu-item>
      </el-submenu>
      <el-menu-item v-else :key="menu.path" :index="menu.path">
        <i :class="menu.icon" />
        <span>{{ menu.name }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script>
export default {
  computed: {
    menus() {
      return this.$store.state.menus;
    },
  },
};
</script> -->
