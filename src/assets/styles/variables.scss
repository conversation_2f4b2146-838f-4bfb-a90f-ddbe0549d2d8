// // base color
// $blue: #324157;
// $light-blue: #3a71a8;
// $red: #c03639;
// $pink: #e65d6e;
// $green: #30b08f;
// $tiffany: #4ab7bd;
// $yellow: #fec171;
// $panGreen: #30b08f;

// // 默认菜单主题风格
// $base-menu-color: #bfcbd9;
// $base-menu-color-active: #f4f4f5;
// $base-menu-background: #658ebc; //#304156;
// $base-logo-title-color: #ffffff;

// $base-menu-light-color: rgba(0, 0, 0, 0.7);
// $base-menu-light-background: #ffffff;
// $base-logo-light-title-color: #001529;

// $base-sub-menu-background: #1f2d3d;
// $base-sub-menu-hover: #001528;

// // 自定义暗色菜单风格

// // $base-menu-color: hsla(0, 0%, 100%, 0.65);
// // $base-menu-color-active: #fff;
// // $base-menu-background: #001529;
// // $base-logo-title-color: #ffffff;

// // $base-menu-light-color: rgba(0, 0, 0, 0.7);
// // $base-menu-light-background: #ffffff;
// // $base-logo-light-title-color: #001529;

// $base-sub-menu-background: #081f35;
// // $base-sub-menu-hover: #001528;

// $base-sidebar-width: 200px;

/**
* 修改菜单主题风格
*/

// 基础颜色
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;

// 默认菜单主题风格
$base-menu-color: #d8dcf0; // 菜单文字颜色：浅紫色
$base-menu-color-active: #ffffff; // 激活文字颜色：纯白色
$base-menu-background: #2b2e4a; // 菜单背景色：深紫蓝色
$base-logo-title-color: #ffffff; // Logo标题颜色

$base-menu-light-color: rgba(0, 0, 0, 0.65); // 浅色主题文字颜色
$base-menu-light-background: #ffffff; // 浅色主题背景色
$base-logo-light-title-color: #001529; // 浅色主题Logo标题颜色

$base-sub-menu-background: #363b64; // 子菜单背景色：稍浅的紫色
$base-sub-menu-hover: #7e57c2; // 悬停/选中颜色：亮紫色

$base-sidebar-width: 200px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuColor: $base-menu-color;
  menuLightColor: $base-menu-light-color;
  menuColorActive: $base-menu-color-active;
  menuBackground: $base-menu-background;
  menuLightBackground: $base-menu-light-background;
  subMenuBackground: $base-sub-menu-background;
  subMenuHover: $base-sub-menu-hover;
  sideBarWidth: $base-sidebar-width;
  logoTitleColor: $base-logo-title-color;
  logoLightTitleColor: $base-logo-light-title-color;
}
