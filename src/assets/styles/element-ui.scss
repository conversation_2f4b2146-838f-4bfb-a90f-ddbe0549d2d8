// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
// .el-dropdown-menu {
//   a {
//     display: block;
//   }
// }

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

// dropdown
.el-dropdown-menu {
  border-radius: 4px;
  padding: 6px 0;

  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 14px;

    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      color: #409eff;
    }
  }
}

// 修改下拉菜单样式，使其与导航栏风格一致
.navbar .el-dropdown-menu {
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-dropdown-menu__item {
    padding: 10px 20px;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
    }
  }
}

// // 添加到文件末尾

// // 表格样式调整，适应一屏显示
// .el-table {
//   height: 100%;
//   display: flex;
//   flex-direction: column;

//   .el-table__header-wrapper {
//     flex-shrink: 0;
//   }

//   .el-table__body-wrapper {
//     flex: 1;
//     overflow: auto;

//     // 美化滚动条
//     &::-webkit-scrollbar {
//       width: 6px;
//       height: 6px;
//     }

//     &::-webkit-scrollbar-thumb {
//       background: rgba(0, 0, 0, 0.1);
//       border-radius: 3px;
//     }

//     &::-webkit-scrollbar-track {
//       background: rgba(0, 0, 0, 0.05);
//     }
//   }

//   .el-table__footer-wrapper {
//     flex-shrink: 0;
//   }
// }

// // 对话框样式调整
// .el-dialog__wrapper {
//   display: flex;
//   align-items: center;
//   justify-content: center;

//   .el-dialog {
//     margin: 0 !important;
//     display: flex;
//     flex-direction: column;
//     max-height: 90vh;

//     .el-dialog__body {
//       flex: 1;
//       overflow: auto;

//       // 美化滚动条
//       &::-webkit-scrollbar {
//         width: 6px;
//         height: 6px;
//       }

//       &::-webkit-scrollbar-thumb {
//         background: rgba(0, 0, 0, 0.1);
//         border-radius: 3px;
//       }
//     }
//   }
// }
