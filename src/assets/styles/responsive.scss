// 基础响应式布局
@media screen and (max-width: 1200px) {
  .app-container {
    padding: 10px;
  }
}

// 平板设备
@media screen and (max-width: 992px) {
  .el-dialog {
    width: 90% !important;
  }

  .hidden-sm-and-down {
    display: none !important;
  }
}

// 手机设备
@media screen and (max-width: 768px) {
  .app-container {
    padding: 5px;
  }

  .el-form-item {
    margin-bottom: 10px;
  }

  .el-card__header {
    padding: 10px;
  }

  .el-card__body {
    padding: 10px;
  }
}

// 高度响应式
@media screen and (max-height: 768px) {
  .dashboard-content {
    .content-section {
      margin-bottom: 10px;
    }

    h1 {
      font-size: 24px;
    }
    h2 {
      font-size: 18px;
    }
    p {
      font-size: 14px;
    }
  }
}

// 小屏幕设备
@media screen and (max-height: 600px) {
  .el-form-item {
    margin-bottom: 8px;
  }
}
