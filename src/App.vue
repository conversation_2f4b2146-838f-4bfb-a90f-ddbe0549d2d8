<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  metaInfo() {
    return {
      // titleTemplate: '%s - OPENWORLD',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      ],
    };
  },
  mounted() {
    // 确保动态路由已经加载
    // this.$store.dispatch("login").catch(() => {
    //   this.$router.push("/login");
    // });
  },

  methods: {},
};
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  height: 100%;
  overflow-y: auto; /* 确保页面可以滚动 */
}
body {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: #f5f5f5;
  line-height: 1.6;
}

#app {
  min-height: 100vh;
  position: relative;
  overflow-y: auto; /* 确保应用容器可以滚动 */
}

/* 预加载关键字体 */
@font-face {
  font-family: 'PingFang SC';
  font-display: swap;
}

/* 图片懒加载优化 */
img {
  max-width: 100%;
  height: auto;
}

img[loading='lazy'] {
  opacity: 0;
  transition: opacity 0.3s;
}

img[loading='lazy'].loaded {
  opacity: 1;
}
</style>
