<!doctype html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="<%= BASE_URL %>favicon.ico" />
    <link rel="icon" type="image/png" sizes="32x32" href="<%= BASE_URL %>favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="<%= BASE_URL %>favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="<%= BASE_URL %>apple-touch-icon.png" />
    <link rel="mask-icon" href="<%= BASE_URL %>safari-pinned-tab.svg" color="#5bbad5" />
    <meta name="msapplication-TileColor" content="#da532c" />
    <meta name="theme-color" content="#ffffff" />

    <title>OPEN-WORLD</title>
    <script>
      (function () {
        // 配置参数
        const titleConfig = {
          original: 'OPEN-WORLD',
          hidden: '开放世界需要你',
          interval: 1500, // 闪烁间隔
          blink: true, // 是否启用闪烁
        };

        let isHidden = false;
        let blinkTimer = null;

        // 保存原始标题
        document.title = titleConfig.original;

        // 核心监听逻辑
        document.addEventListener('visibilitychange', () => {
          isHidden = document.hidden;

          if (isHidden) {
            activateBlinkEffect();
          } else {
            restoreTitle();
          }
        });
        // 启用闪烁效果
        function activateBlinkEffect() {
          if (!titleConfig.blink) {
            document.title = titleConfig.hidden;
            return;
          }

          let count = 0;
          const symbols = ['»', '»»', '»»»'];

          blinkTimer = setInterval(() => {
            count = (count + 1) % symbols.length;
            document.title = `${symbols[count]} ${titleConfig.hidden} ${symbols[count]}`;
          }, titleConfig.interval);
        }

        // 恢复原始标题
        function restoreTitle() {
          document.title = titleConfig.original;
          clearInterval(blinkTimer);
        }

        // 页面关闭前清理
        window.addEventListener('beforeunload', () => {
          clearInterval(blinkTimer);
        });
      })();
    </script>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without
        JavaScript enabled. Please enable it to continue.</strong
      >
    </noscript>
    <div id="app"></div>
  </body>
</html>
