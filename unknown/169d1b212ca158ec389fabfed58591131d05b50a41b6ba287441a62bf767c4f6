// const { defineConfig } = require("@vue/cli-service");
// const path = require("path");
// module.exports = defineConfig({
//   chainWebpack(config) {
//     // SVG 处理规则
//     config.module
//       .rule("svg")
//       .exclude.add(path.resolve(__dirname, "src/icons"))
//       .end();

//     config.module
//       .rule("icons")
//       .test(/\.svg$/)
//       .include.add(path.resolve(__dirname, "src/icons"))
//       .end()
//       .use("svg-sprite-loader")
//       .loader("svg-sprite-loader")
//       .options({
//         symbolId: "icon-[name]",
//       })
//       .end();
//   },
//   devServer: {
//     port: 9876,
//     proxy: {
//       [process.env.VUE_APP_BASE_API]: {
//         target: "http://localhost:3000", // 将请求代理到本地 3000 端口
//         changeOrigin: true,
//         pathRewrite: { ["^" + process.env.VUE_APP_BASE_API]: "" }, // 可选：重写路径（移除 /api 前缀）
//       },
//     },
//   },
//   // transpileDependencies: true,
//   transpileDependencies: ["vue-particles"],
// });
"use strict";
const path = require("path");
const CompressionPlugin = require("compression-webpack-plugin");
const webpack = require("webpack");
// 用于快速定位项目中对应路径的简化函数
function resolve(dir) {
  return path.join(__dirname, dir);
}
// 网页标题，优先使用环境变量 VUE_APP_TITLE，否则使用默认值
const name = process.env.VUE_APP_TITLE || "OPEN-WORLD";
// 开发服务器端口，优先使用环境变量 port 或 npm_config_port，否则使用默认端口 1014
const port = process.env.port || process.env.npm_config_port || 1014;
module.exports = {
  // 部署应用的基本 URL
  // production 下假设部署在根路径，否则均使用根路径
  publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
  // 打包生成的目标文件夹名称，建议和 publicPath 匹配
  outputDir: "dist",
  // 放置静态资源（js, css, img, fonts）的文件夹名称
  assetsDir: "static",
  // 开发环境下开启 eslint 检查
  lintOnSave: process.env.NODE_ENV === "development",
  // 生产环境是否生成 source map 文件
  productionSourceMap: false,
  // webpack-dev-server 相关配置
  devServer: {
    host: "0.0.0.0", // 允许局域网访问
    port: port,
    // open: true, // 启动后自动打开浏览器
    // 代理配置（用于跨域解决方案）
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: "http://localhost:3000",
        // target: "http://*************:3000",
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: "",
        },
      },
    },
    // 关闭 Uncaught error 的全屏提示
    client: {
      overlay: false,
    },
    // disableHostCheck: true,
    // allowedHosts: "all", // 允许所有 host 访问
  },
  // CSS 相关配置
  css: {
    loaderOptions: {
      sass: {
        sassOptions: {
          // 输出方式设置为 expanded 以便调试
          outputStyle: "expanded",
        },
      },
    },
  },
  // 配置 webpack 底层设置
  configureWebpack: {
    // 在页面中可通过 title 获取网站标题
    name: name,
    resolve: {
      // 设置路径别名，可用 @ 代表 src 目录
      alias: {
        "@": resolve("src"),
      },
    },
    plugins: [
      // 自动注入 process 变量
      new webpack.ProvidePlugin({
        process: "process/browser",
      }),
      new CompressionPlugin({
        test: /\.(js|css|html)?/i, // 压缩文件格式
        filename: "[path][base].gz[query]", // 压缩后的文件名
        algorithm: "gzip", // 使用 gzip 算法
        minRatio: 0.8, // 当压缩率小于 0.8 时才进行压缩
      }),
    ],
    // plugins: [
    //   // 使用 gzip 压缩生产环境的静态文件
    //   new CompressionPlugin({
    //     cache: false, // 禁用文件缓存，确保每次构建都压缩
    //     test: /.(js|css|html)?$/i, // 指定需要压缩的文件类型
    //     filename: "[path].gz[query]", // 压缩后文件的命名方式
    //     algorithm: "gzip", // 使用 gzip 算法
    //     minRatio: 0.8, // 只有压缩率小于 0.8 时才会进行压缩
    //   }),
    // ],
  },
  // 通过 webpack-chain 对构建流程进行更细粒度的配置
  chainWebpack(config) {
    // 删除预加载和预取插件以优化首屏加载
    config.plugins.delete("preload");
    config.plugins.delete("prefetch");
    // 设置 svg-sprite-loader 规则：将 src/assets/icons 内的 svg 文件打包成 svg sprite
    config.module.rule("svg").exclude.add(resolve("src/assets/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        // 配置打包后 svg sprite 中的 symbol id 格式
        symbolId: "icon-[name]",
      })
      .end();

    // 针对生产环境的配置
    config.when(process.env.NODE_ENV !== "development", (config) => {
      // 使用 ScriptExtHtmlWebpackPlugin 将 runtime 内联到 HTML 中
      config
        .plugin("ScriptExtHtmlWebpackPlugin")
        .after("html")
        .use("script-ext-html-webpack-plugin", [
          {
            // inline 匹配 runtimeChunk 生成的 runtime 文件
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();

      // 优化 webpack splitChunks 配置，将第三方库和自定义组件拆分打包
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          // 将 node_modules 中的库打包到 chunk-libs 中
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial", // 仅初始依赖的第三方库
          },
          // 单独将 element-ui 打包为 chunk-elementUI
          elementUI: {
            name: "chunk-elementUI",
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
            priority: 20,
          },
          // 将 src/components 中复用次数较多的组件打包到 chunk-commons 中
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"),
            minChunks: 3,
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      });

      // 将 webpack runtime 单独打包成一个 chunk
      config.optimization.runtimeChunk("single");

      // 可选：复制 robots.txt 到打包目录根下（防爬虫等用途），根据需要配置插件实现复制操作
      // 一般可通过 copy-webpack-plugin 实现，示例如下：
      // config
      //   .plugin("copy")
      //   .use(require("copy-webpack-plugin"), [
      //     {
      //       patterns: [
      //         {
      //           from: path.resolve(__dirname, "./public/robots.txt"),
      //           to: "./",
      //         },
      //       ],
      //     },
      //   ])
      //   .end();
    });
  },
};
