<template>
  <div class="login-container">
    <!-- 背景粒子效果 -->
    <vue-particles
      class="particles"
      color="#409EFF"
      :particleOpacity="0.7"
      :particlesNumber="60"
      shapeType="circle"
      :particleSize="4"
      linesColor="#409EFF"
      :linesWidth="1"
      :lineLinked="true"
      :lineOpacity="0.4"
      :linesDistance="150"
      :moveSpeed="3"
      :hoverEffect="true"
      hoverMode="grab"
      :clickEffect="true"
      clickMode="push"
    />

    <!-- 登录表单卡片 -->
    <el-card class="login-box animated fadeIn">
      <div class="logo-container">
        <img src="@/assets/logo.png" class="logo" alt="logo" />
        <h1 class="title">欢迎登录</h1>
      </div>
      <!-- <div class="login-card">
      <h1 class="login-title">欢迎登录</h1> -->

      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        @keyup.enter.native="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="el-icon-user"
          >
            <i slot="prefix" class="iconfont icon-user input-icon" />
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock"
            show-password
          >
            <i slot="prefix" class="iconfont icon-lock input-icon" />
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            :loading="loading"
            type="primary"
            class="login-btn"
            @click="handleLogin"
          >
            登 录
          </el-button>
        </el-form-item>

        <div class="footer">
          <el-checkbox v-model="rememberMe">记住密码</el-checkbox>
          <el-link type="info" :underline="false">忘记密码?</el-link>
          <el-link type="primary" class="register-link" @click="goToRegister">
            立即注册
          </el-link>
        </div>
      </el-form>
      <div class="oauth-divider">
        <span class="line"></span>
        <span class="text">第三方登录</span>
        <span class="line"></span>
      </div>
      <div class="oauth-buttons">
        <el-button class="wechat-btn" @click="socialLogin('wechat')">
          <svg-icon icon-class="wechat" />
          微信登录
        </el-button>

        <el-button class="github-btn" @click="socialLogin('github')">
          <svg-icon icon-class="github" />
          GitHub登录
        </el-button>
      </div>
      <!-- </div> -->
    </el-card>
  </div>
</template>

<script>
// import { socialAuth } from '@/api/auth'
export default {
  name: "MyLogin",
  data() {
    return {
      loginForm: {
        username: "admin",
        password: "123456",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" },
          { min: 6, max: 20, message: "长度在6到20个字符", trigger: "blur" },
        ],
      },
      loading: false,
      rememberMe: false,
    };
  },
  methods: {
    goToRegister() {
      this.$router.push("/register");
    },
    async socialLogin(provider) {
      try {
        // 打开新窗口进行OAuth认证
        const authWindow = window.open("", "_blank", "width=600,height=800");

        const {
          data: { authUrl },
        } = await this.$axios.get(`/auth/${provider}`, {
          params: { redirect: window.location.origin },
        });

        authWindow.location.href = authUrl;

        // 监听消息回调
        window.addEventListener(
          "message",
          (e) => {
            if (e.origin !== window.location.origin) return;

            if (e.data.type === "oauth-success") {
              this.handleLoginSuccess(e.data.token);
              authWindow.close();
            }
          },
          false
        );
      } catch (error) {
        this.$message.error("第三方登录失败");
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$store
            .dispatch("login", this.loginForm)
            .then(() => {
              this.$router.push("/");
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/* 全局滚动条隐藏 */
html {
  overflow: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}
::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}
.oauth-divider {
  display: flex;
  align-items: center;
  margin: 30px 0;

  .line {
    flex: 1;
    height: 1px;
    background: #dcdfe6;
  }

  .text {
    padding: 0 15px;
    color: #909399;
    font-size: 14px;
  }
}

.oauth-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;

  .el-button {
    width: 180px;
    padding: 12px;
    border-radius: 25px;

    .svg-icon {
      margin-right: 8px;
      font-size: 18px;
      vertical-align: middle;
    }
  }

  .wechat-btn {
    background: #09bb07;
    border-color: #09bb07;
    color: white;

    &:hover {
      opacity: 0.9;
    }
  }

  .github-btn {
    background: #333;
    border-color: #333;
    color: white;
  }
}
.login-container {
  /* 视口全屏布局 */
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  // background: linear-gradient(45deg, #1e3c72, #2a5298);
  background: #f0f2f5;
  /* 防止内容溢出 */
  overflow: hidden;
  position: relative;

  .particles {
    height: 100%;
  }

  .login-box {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    // width: 450px;
    width: 400px;
    // padding: 35px 35px 15px;
    padding: 40px;
    // border-radius: 10px;
    border-radius: 8px;
    // box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    // background: rgba(255, 255, 255, 0.95);
    background: white;

    /* 移动端适配 */
    @media (max-width: 480px) {
      .footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;

        .auth-links {
          width: 100%;
          justify-content: space-between;
        }
      }
      width: 90%;
      padding: 20px;
    }

    .logo-container {
      text-align: center;
      margin-bottom: 30px;

      .logo {
        width: 80px;
        height: 80px;
        margin-bottom: 15px;
        transition: all 0.3s;

        &:hover {
          transform: rotate(360deg);
        }
      }

      .title {
        font-size: 24px;
        color: #303133;
        margin: 0;
        letter-spacing: 2px;
      }
    }

    ::v-deep .el-input__inner {
      height: 48px;
      line-height: 48px;
      padding-left: 40px;
      border-radius: 24px;
    }

    .input-icon {
      color: #889aa4;
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;
      font-size: 18px;
    }

    .login-btn {
      width: 100%;
      height: 48px;
      font-size: 16px;
      letter-spacing: 4px;
      border-radius: 24px;
      background: linear-gradient(45deg, #1e3c72, #2a5298);
      border: none;
      transition: all 0.3s;

      &:hover {
        opacity: 0.9;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(32, 160, 255, 0.4);
      }
    }

    .footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;

      .auth-links {
        display: flex;
        gap: 15px;

        .register-link {
          font-weight: 500;
          &::after {
            content: "|";
            color: #dcdfe6;
            margin-left: 10px;
          }
        }
      }
    }
  }
}

// 入场动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -45%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}
.animated {
  animation-duration: 0.5s;
  animation-fill-mode: both;
}
.fadeIn {
  animation-name: fadeIn;
}
</style>
