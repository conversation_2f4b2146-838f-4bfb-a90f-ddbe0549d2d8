<template>
  <div
    id="app"
    class="chat-app"
  >
    <el-container class="full-height">
      <el-aside
        width="350px"
        class="chat-list-aside"
      >
        <div class="chat-list-header">
          <h2>Chats</h2>
          <div class="header-actions-group">
            <!-- 好友申请图标 -->
            <el-badge
              :value="pendingFriendRequestCount"
              :max="99"
              :hidden="pendingFriendRequestCount === 0"
              class="header-action-badge"
            >
              <el-button
                icon="el-icon-user-solid"
                circle
                title="好友请求"
                size="mini"
                @click="handleFriendRequests"
              />
            </el-badge>
            <!-- 新建好友、新建群组 -->
            <el-dropdown
              style="margin-left: 10px"
              @command="handleHeaderCommand"
            >
              <el-button
                type="primary"
                size="mini"
              >
                新建
                <i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="addFriend">
                  添加好友
                </el-dropdown-item>
                <el-dropdown-item command="createGroup">
                  创建群组
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <!-- 好友列表 -->
        <el-menu
          :default-active="currentChat ? currentChat.chatId : ''"
          class="chat-el-menu"
          @select="handleSelectChat"
        >
          <el-menu-item
            v-if="chatsWithDetails.length === 0"
            index="no-chats"
            disabled
          >
            <div class="no-chats-item">
              暂无聊天
            </div>
          </el-menu-item>
          <el-menu-item
            v-for="chat in chatsWithDetails"
            :key="chat.chatId"
            :index="chat.chatId"
            class="chat-list-item-padding"
            :class="{ 'pinned-chat-item': chat.isPinned }"
          >
            <div class="chat-item-detailed">
              <el-badge
                :value="chat.unreadCount"
                :max="99"
                :hidden="!chat.unreadCount"
                class="chat-list-badge"
              >
                <el-avatar
                  :size="40"
                  class="chat-avatar"
                  :style="{ backgroundColor: chat.displayAvatarColor }"
                >
                  <i
                    v-if="chat.type === 'group' && !chat.displayAvatar"
                    class="el-icon-user-solid"
                  />
                  <span v-else>{{
                    chat.displayAvatar ||
                      (chat.displayName ? chat.displayName[0] : "?")
                  }}</span>
                </el-avatar>
              </el-badge>
              <div class="chat-info">
                <div class="chat-info-header">
                  <span class="chat-name">{{ chat.displayName }}</span>
                  <span class="latest-message-time">{{
                    chat.latestMessageTime
                  }}</span>
                </div>
                <p
                  class="latest-message-text"
                  :title="chat.fullLatestMessageText"
                  v-html="chat.latestMessageSnippet"
                />
              </div>
              <el-button
                class="pin-chat-button"
                :type="chat.isPinned ? 'warning' : 'info'"
                :icon="chat.isPinned ? 'el-icon-star-on' : 'el-icon-star-off'"
                size="mini"
                circle
                plain
                :title="chat.isPinned ? '取消置顶' : '置顶会话'"
                @click.stop="togglePinChat(chat)"
              />
            </div>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <el-main class="chat-main no-padding">
        <div
          v-if="currentChat"
          class="chat-window"
        >
          <div class="chat-header">
            <h3>
              {{ currentChat.displayName }}
              <i
                v-if="currentChat.isPinned"
                class="el-icon-star-on pinned-chat-indicator"
                title="已置顶"
              />
              <span
                v-if="currentChat.type === '1'"
                class="group-member-count"
              >({{
                currentChat.members ? currentChat.members.length : 0
              }}人)</span>
            </h3>
            <div class="chat-header-actions">
              <el-button
                v-if="currentChat.type === '0'"
                type="primary"
                plain
                icon="el-icon-edit"
                size="mini"
                @click="handleEditFriendDialog(currentChat.id)"
              >
                编辑好友
              </el-button>
              <el-button
                v-if="currentChat.type === '1'"
                type="success"
                plain
                icon="el-icon-setting"
                size="mini"
                @click="handleEditGroupDialog(currentChat.id)"
              >
                群设置
              </el-button>
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                @click="handleDeleteCurrentChat"
              >
                删除会话
              </el-button>
            </div>
          </div>
          <div
            ref="chatMessages"
            class="chat-messages"
          >
            <div
              v-for="(msg, index) in currentChatMessages"
              :key="msg.msgId || index"
              :class="[
                'message-item',
                msg.type === 'system' ? 'system-message-item' : (msg.sender === myId ? 'sent-item' : 'received-item'),
              ]"
            >
              <!-- 系统消息显示 -->
              <div
                v-if="msg.type === 'system'"
                class="system-message"
              >
                <span class="system-message-content">{{ msg.content }}</span>
                <span class="message-time">{{ msg.time }}</span>
              </div>
              <template v-else>
                <el-avatar
                  v-if="msg.sender !== myId"
                  :size="36"
                  class="message-avatar"
                  :style="{ backgroundColor: getSenderAvatarColor(msg.sender) }"
                >
                  {{ getSenderAvatarText(msg.sender) }}
                </el-avatar>
                <div :class="['message-bubble-wrapper']">
                  <span
                    v-if="currentChat.type === '1' && msg.sender !== myId"
                    class="message-sender-name"
                  >{{ getSenderName(msg.sender) }}</span>
                  <div
                    :class="[
                      'message-bubble',
                      msg.sender === myId ? 'sent' : 'received',
                      `message-type-${msg.type}`,
                    ]"
                  >
                    <p
                      v-if="msg.type === 'text'"
                      class="message-text"
                    >
                      {{ msg.content }}
                    </p>
                    <p
                      v-if="msg.type === 'emoji'"
                      class="message-emoji"
                    >
                      {{ msg.content }}
                    </p>
                    <div
                      v-if="msg.type === 'file'"
                      class="message-file"
                    >
                      <img
                        v-if="msg.file.isImage && msg.file.previewUrl"
                        :src="msg.file.previewUrl"
                        :alt="msg.file.name"
                        class="file-image-preview"
                      >
                      <i
                        v-else
                        :class="getFileIcon(msg.file.name)"
                        class="file-icon"
                      />
                      <div class="file-info">
                        <span
                          class="file-name"
                          :title="msg.file.name"
                        >{{
                          msg.file.name
                        }}</span>
                        <span class="file-size">{{
                          formatFileSize(msg.file.size)
                        }}</span>
                      </div>
                      <el-button
                        type="text"
                        size="mini"
                        class="file-download-button"
                        @click="mockDownload(msg.file)"
                      >
                        下载
                      </el-button>
                    </div>
                    <span class="message-time">{{ msg.time }}</span>
                  </div>
                </div>
                <el-avatar
                  v-if="msg.sender === myId"
                  :size="36"
                  class="message-avatar my-avatar"
                  :style="{ backgroundColor: myAvatarColor }"
                >
                  {{ myAvatar }}
                </el-avatar>
              </template>
            </div>
            <div
              v-if="currentChatMessages.length === 0"
              class="no-messages"
            >
              开始聊天吧！
            </div>
          </div>
          <div class="chat-input-area">
            <div class="chat-actions">
              <el-popover
                v-model="emojiPickerVisible"
                placement="top-start"
                width="280"
                trigger="click"
              >
                <div class="emoji-picker">
                  <span
                    v-for="emoji in availableEmojis"
                    :key="emoji"
                    class="emoji-char"
                    @click="sendEmoji(emoji)"
                  >{{ emoji }}</span>
                </div>
                <el-button
                  slot="reference"
                  icon="el-icon-sugar"
                  circle
                  title="表情"
                />
              </el-popover>
              <el-button
                icon="el-icon-folder-add"
                circle
                title="文件"
                @click="triggerFileInput"
              />
              <input
                ref="fileInput"
                type="file"
                style="display: none"
                @change="handleFileSelected"
              >
            </div>
            <el-input
              v-model="newMessageText"
              class="text-input"
              type="textarea"
              :rows="2"
              placeholder="输入消息..."
              @keyup.enter.native="sendTextMessage"
            />
            <el-button
              class="send-button"
              type="primary"
              :disabled="!newMessageText.trim()"
              @click="sendTextMessage"
            >
              发送
            </el-button>
          </div>
        </div>
        <div
          v-else
          class="no-chat-selected"
        >
          <p>选择一个会话开始聊天，或创建一个新的群组/好友聊天。</p>
        </div>
      </el-main>
    </el-container>

    <!-- <el-dialog
      :title="friendDialogTitle"
      :visible.sync="friendDialogVisible"
      width="350px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form
        ref="friendForm"
        :model="friendForm"
        label-width="80px"
      >
        <el-form-item
          label="用户名"
          prop="searchName"
        >
          <div class="search-user-container">
            <el-input
              v-model="friendForm.searchName"
              placeholder="输入用户名查询"
            />
            <el-button
              type="primary"
              :loading="searching"
              @click="searchUser"
            >
              查询
            </el-button>
          </div>
        </el-form-item>

        <div
          v-if="searchResult"
          class="search-result"
        >
          <div
            v-if="searchResult.found"
            class="user-found"
          >
            <el-avatar
              :size="50"
              :style="{ backgroundColor: searchResult.avatarColor }"
            >
              {{ searchResult.avatar || searchResult.name[0] }}
            </el-avatar>
            <div class="user-info">
              <span class="user-name">{{ searchResult.name }}</span>
            </div>
          </div>
          <div
            v-else
            class="user-not-found"
          >
            <i class="el-icon-warning-outline" />
            <span>查无此用户</span>
          </div>
        </div>
        <el-form-item
          v-if="showRemarkInput"
          label="备注"
          prop="remark"
        >
          <el-input
            v-model="friendForm.applyInfo"
            placeholder="请输入备注信息（选填）"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancelAddFriend">
          取 消
        </el-button>
        <el-button
          v-if="!showRemarkInput"
          type="primary"
          :disabled="!searchResult || !searchResult.found"
          @click="prepareAddFriend"
        >
          添加好友
        </el-button>
        <el-button
          v-else
          type="primary"
          @click="sendFriendRequest"
        >
          确认发送
        </el-button>
      </div>
    </el-dialog> -->

    <el-dialog
      :title="friendDialogTitle"
      :visible.sync="friendDialogVisible"
      width="350px"
      :close-on-click-modal="false"
      append-to-body
    >
      <!-- 添加标签页切换 -->
      <el-tabs v-model="activeTab">
        <el-tab-pane
          label="添加好友"
          name="addFriend"
        >
          <el-form
            ref="friendForm"
            :model="friendForm"
            label-width="80px"
          >
            <el-form-item
              label="用户名"
              prop="searchName"
            >
              <div class="search-user-container">
                <el-input
                  v-model="friendForm.searchName"
                  placeholder="输入用户名查询"
                />
                <el-button
                  type="primary"
                  :loading="searching"
                  @click="searchUser"
                >
                  查询
                </el-button>
              </div>
            </el-form-item>

            <!-- 搜索结果显示 -->
            <div
              v-if="searchResult"
              class="search-result"
            >
              <div
                v-if="searchResult.found"
                class="user-found"
              >
                <el-avatar
                  :size="50"
                  :style="{ backgroundColor: searchResult.avatarColor }"
                >
                  {{ searchResult.avatar || searchResult.name[0] }}
                </el-avatar>
                <div class="user-info">
                  <span class="user-name">{{ searchResult.name }}</span>
                </div>
              </div>
              <div
                v-else
                class="user-not-found"
              >
                <i class="el-icon-warning-outline" />
                <span>查无此用户</span>
              </div>
            </div>

            <!-- 添加备注输入框，仅在找到用户且点击了添加按钮后显示 -->
            <el-form-item
              v-if="showRemarkInput"
              label="备注"
              prop="remark"
            >
              <el-input
                v-model="friendForm.applyInfo"
                placeholder="请输入备注信息（选填）"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
          </el-form>
          <div class="dialog-footer">
            <el-button @click="cancelAddFriend">
              取 消
            </el-button>
            <el-button
              v-if="!showRemarkInput"
              type="primary"
              :disabled="!searchResult || !searchResult.found"
              @click="prepareAddFriend"
            >
              添加好友
            </el-button>
            <el-button
              v-else
              type="primary"
              @click="sendFriendRequest"
            >
              确认发送
            </el-button>
          </div>
        </el-tab-pane>
    
        <!-- 新增：申请入群标签页 -->
        <el-tab-pane
          label="申请入群"
          name="joinGroup"
        >
          <el-form
            ref="groupJoinForm"
            :model="groupJoinForm"
            label-width="80px"
          >
            <el-form-item
              label="群名称"
              prop="searchGroupName"
            >
              <div class="search-user-container">
                <el-input
                  v-model="groupJoinForm.searchGroupName"
                  placeholder="输入群名称查询"
                />
                <el-button
                  type="primary"
                  :loading="searchingGroup"
                  @click="searchGroup"
                >
                  查询
                </el-button>
              </div>
            </el-form-item>

            <!-- 群组搜索结果显示 -->
            <div
              v-if="groupSearchResult"
              class="search-result"
            >
              <div
                v-if="groupSearchResult.found"
                class="user-found"
              >
                <el-avatar
                  :size="50"
                  :style="{ backgroundColor: groupSearchResult.avatarColor }"
                >
                  {{ groupSearchResult.avatar }}
                </el-avatar>
                <div class="user-info">
                  <div class="user-name">
                    {{ groupSearchResult.name }}
                  </div>
                  <div class="group-member-count">
                    成员数: {{ groupSearchResult.memberCount || 0 }}
                  </div>
                </div>
              </div>
              <div
                v-else
                class="user-not-found"
              >
                未找到该群组
              </div>
            </div>

            <!-- 申请入群理由，仅在找到群组且点击了申请按钮后显示 -->
            <el-form-item
              v-if="showGroupRemarkInput"
              label="申请理由"
              prop="applyReason"
            >
              <el-input
                v-model="groupJoinForm.applyInfo"
                placeholder="请输入申请理由（选填）"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
          </el-form>
          <div class="dialog-footer">
            <el-button @click="cancelJoinGroup">
              取 消
            </el-button>
            <el-button
              v-if="!showGroupRemarkInput"
              type="primary"
              :disabled="!groupSearchResult || !groupSearchResult.found"
              @click="prepareJoinGroup"
            >
              申请入群
            </el-button>
            <el-button
              v-else
              type="primary"
              @click="sendGroupJoinRequest"
            >
              确认发送
            </el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog
      :title="groupDialogTitle"
      :visible.sync="groupDialogVisible"
      width="450px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form
        ref="groupForm"
        :model="groupForm"
        :rules="groupFormRules"
        label-width="80px"
      >
        <el-form-item
          label="群名称"
          prop="name"
        >
          <el-input
            v-model="groupForm.name"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item
          label="群头像"
          prop="avatar"
        >
          <el-input
            v-model="groupForm.avatar"
            autocomplete="off"
            placeholder="文字/Emoji或留空"
          />
        </el-form-item>
        <el-form-item
          label="群成员"
          prop="members"
        >
          <el-select
            v-model="groupForm.members"
            multiple
            filterable
            placeholder="请选择群成员"
            style="width: 100%"
          >
            <el-option
              v-for="friend in friends"
              :key="friend.id"
              :label="friend.name"
              :value="friend.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="置顶"
          prop="isPinned"
        >
          <el-switch v-model="groupForm.isPinned" />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="groupDialogVisible = false">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="submitGroupForm"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- <el-dialog
      title="好友请求"
      :visible.sync="friendRequestsDialogVisible"
      width="500px"
      append-to-body
    >
      <div
        v-if="pendingFriendRequests.length === 0"
        class="no-requests"
      >
        暂无新的好友请求
      </div>
      <el-card
        v-for="req in pendingFriendRequests"
        :key="req.id"
        class="request-card"
        shadow="hover"
      >
        <div class="request-item">
          <el-avatar
            :size="45"
            :style="{
              backgroundColor: req.senderAvatarColor,
              marginRight: '15px',
            }"
          >
            {{ req.senderAvatar || req.senderName[0] }}
          </el-avatar>
          <div class="request-info">
            <span class="request-sender-name">{{ req.senderName }}</span>
            <p
              v-if="req.message"
              class="request-message"
            >
              "{{ req.message }}"
            </p>
            <p
              v-else
              class="request-message-empty"
            >
              <i>对方没有留言</i>
            </p>
          </div>
          <div class="request-actions">
            <el-button
              type="success"
              size="mini"
              @click="acceptFriendRequest(req.id, req.senderName)"
            >
              同意
            </el-button>
            <el-button
              type="danger"
              plain
              size="mini"
              @click="declineFriendRequest(req.id)"
            >
              拒绝
            </el-button>
          </div>
        </div>
      </el-card>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="friendRequestsDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog> -->
  
    <el-dialog
      title="申请列表"
      :visible.sync="friendRequestsDialogVisible"
      width="500px"
      append-to-body
    >
      <!-- 添加标签页切换 -->
      <el-tabs v-model="requestActiveTab">
        <el-tab-pane
          label="好友申请"
          name="friendRequests"
        >
          <div
            v-if="pendingFriendRequests.length === 0"
            class="no-requests"
          >
            暂无新的好友请求
          </div>
          <el-card
            v-for="req in pendingFriendRequests"
            :key="req.id"
            class="request-card"
            shadow="hover"
          >
            <div class="request-item">
              <el-avatar
                :size="45"
                :style="{
                  backgroundColor: req.senderAvatarColor,
                  marginRight: '15px',
                }"
              >
                {{ req.senderAvatar || req.senderName[0] }}
              </el-avatar>
              <div class="request-info">
                <span class="request-sender-name">{{ req.senderName }}</span>
                <p
                  v-if="req.message"
                  class="request-message"
                >
                  "{{ req.message }}"
                </p>
                <p
                  v-else
                  class="request-message-empty"
                >
                  <i>对方没有留言</i>
                </p>
              </div>
              <div class="request-actions">
                <el-button
                  type="success"
                  size="mini"
                  @click="acceptFriendRequest(req.id, req.senderName)"
                >
                  同意
                </el-button>
                <el-button
                  type="danger"
                  plain
                  size="mini"
                  @click="declineFriendRequest(req.id)"
                >
                  拒绝
                </el-button>
              </div>
            </div>
          </el-card>
        </el-tab-pane>
    
        <el-tab-pane
          label="群申请"
          name="groupRequests"
        >
          <div
            v-if="pendingGroupRequests.length === 0"
            class="no-requests"
          >
            暂无新的入群申请
          </div>
          <el-card
            v-for="req in pendingGroupRequests"
            :key="req.id"
            class="request-card"
            shadow="hover"
          >
            <div class="request-item">
              <el-avatar
                :size="45"
                :style="{
                  backgroundColor: req.senderAvatarColor,
                  marginRight: '15px',
                }"
              >
                {{ req.senderAvatar || req.senderName[0] }}
              </el-avatar>
              <div class="request-info">
                <span class="request-sender-name">{{ req.senderName }}</span>
                <span class="request-group-name">申请加入: {{ req.groupName }}</span>
                <p
                  v-if="req.message"
                  class="request-message"
                >
                  "{{ req.message }}"
                </p>
                <p
                  v-else
                  class="request-message-empty"
                >
                  <i>对方没有留言</i>
                </p>
              </div>
              <!-- , req.groupId -->
              <div class="request-actions">
                <el-button
                  type="success"
                  size="mini"
                  @click="acceptFriendRequest(req.id, req.senderName)"
                >
                  同意
                </el-button>
                <el-button
                  type="danger"
                  plain
                  size="mini"
                  @click="declineGroupRequest(req.id)"
                >
                  拒绝
                </el-button>
              </div>
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
  
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="friendRequestsDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { initWs, onMessage } from "@/utils/wsClient";
import { getUserByAccountName } from "@/api/system/user";
import {
  sendFriendRequest,
  getFriendRequest,
  handleFriendRequest,
  handleGroupRequest,
  searchGroupByName
} from "@/api/system/chat";

// const MY_USER_ID = "me";
const getRandomColor = () =>
  `#` +
  Math.floor(Math.random() * ********)
    .toString(16)
    .padStart(6, "0");
let messageIdCounter = 0;
// let friendRequestIdCounter = 0;

export default {
  name: "App",
  data() {
    return {
      // 申请列表标签页控制
    requestActiveTab: 'friendRequests',
      // 添加标签页控制
      activeTab: 'addFriend',
    // 群组搜索相关
    groupJoinForm: {
      searchGroupName: '',
      applyInfo: '',
    },
    searchingGroup: false,
    groupSearchResult: null,
    showGroupRemarkInput: false,

      
      friendApplyCount: 0,
      // myId: MY_USER_ID,
      myId: this.$store.getters.id,
      myAvatar: "我",
      myAvatarColor: "#007bff",
      friends: [], //initialFriends
      groups: [], //initialGroups
      currentChat: null,
      chatHistory: {},
      unreadCounts: {},
      friendRequests: [], // Store all requests  initialFriendRequests
      friendRequestsDialogVisible: false, // For the requests dialog

      newMessageText: "",
      emojiPickerVisible: false,
      availableEmojis: [
        "😊",
        "😂",
        "❤️",
        "👍",
        "🎉",
        "🤔",
        "😢",
        "😮",
        "🙏",
        "🔥",
        "💯",
        "👋",
      ],

      friendDialogVisible: false,
      friendDialogTitle: "",
      friendForm: {
        id: null,
        name: "",
        avatar: "",
        applyInfo: "",
        avatarColor: "",
        searchName: "", // 新增：用于搜索用户的名称
      },
      searching: false, // 新增：表示是否正在搜索
      searchResult: null, // 新增：搜索结果
      showRemarkInput: false, // 新增：控制是否显示备注输入框

      friendFormRules: {
        name: [{ required: true, message: "请输入好友昵称", trigger: "blur" }],
      },
      nextFriendIdSuffix: 3, // Start after initial friends

      groupDialogVisible: false,
      groupDialogTitle: "",
      groupForm: {
        id: this.myId,
        name: "",
        avatar: "",
        members: [],
        avatarColor: "",
        isPinned: false,
      },
      groupFormRules: {
        name: [{ required: true, message: "请输入群名称", trigger: "blur" }],
        members: [
          {
            type: "array",
            required: true,
            min: 1,
            message: "请至少选择一位群成员",
            trigger: "change",
          },
        ],
      },
      nextGroupIdSuffix: 2,

      backgroundMessageInterval: null,
    };
  },
  computed: {
    // ... (currentChatMessages, chatsWithDetails remain largely the same as previous step)
    currentChatMessages() {
      if (this.currentChat && this.chatHistory[this.currentChat.chatId]) {
        return this.chatHistory[this.currentChat.chatId];
      }
      return [];
    },
    chatsWithDetails() {
      //单聊
      const dmChats = this.friends.map((friend) => ({
        chatId: friend.sessionId,
        id: friend.id,
        displayName: friend.name,
        displayAvatar: friend.avatar,
        displayAvatarColor: friend.avatarColor || getRandomColor(),
        type: friend.type,
        isPinned: friend.isPinned || false,
      }));
      //群聊
      const groupChats = this.groups.map((group) => ({
        chatId: group.sessionId,
        id: group.id,
        displayName: group.name,
        displayAvatar: group.avatar,
        displayAvatarColor: group.avatarColor || getRandomColor(),
        type: group.type,
        members: group.members,
        isPinned: group.isPinned || false,
      }));
      const allChats = [...dmChats, ...groupChats];
      return allChats
        .map((chat) => {
          const history = this.chatHistory[chat.chatId];
          let latestMessageSnippet = "暂无消息",
            fullLatestMessageText = "暂无消息",
            latestMessageTime = "";
          if (history && history.length > 0) {
            const lastMsg = history[history.length - 1];
            latestMessageTime = lastMsg.time;
            let prefix = "";
            if (chat.type === "1" && lastMsg.sender !== this.myId) {
              prefix = `${this.getSenderName(lastMsg.sender, "系统")}: `;
            } else if (lastMsg.sender === this.myId && chat.type === "0")
              prefix = "我: ";
            switch (lastMsg.type) {
              case "text":
                latestMessageSnippet = prefix + lastMsg.content;
                break;
              case "emoji":
                latestMessageSnippet =
                  prefix +
                  `<span class="emoji-snippet">${lastMsg.content}</span>`;
                break;
              case "media":
                latestMessageSnippet =
                  prefix +
                  `[${lastMsg.file.isImage ? "图片" : "文件"}] ${
                    lastMsg.file.name
                  }`;
                break;
              default:
                latestMessageSnippet = prefix + "新消息";
            }
            fullLatestMessageText = latestMessageSnippet.replace(
              /<[^>]*>?/gm,
              ""
            ); // Tooltip without HTML
            if (fullLatestMessageText.length > 25) {
              latestMessageSnippet =
                latestMessageSnippet.substring(
                  0,
                  25 +
                    prefix.length +
                    (latestMessageSnippet.includes("<span") ? 20 : 0)
                ) + "...";
            }
          }
          return {
            ...chat,
            latestMessageSnippet,
            fullLatestMessageText,
            latestMessageTime,
            unreadCount: this.unreadCounts[chat.chatId] || 0,
          };
        })
        .sort((a, b) => {
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          if (a.unreadCount > 0 && b.unreadCount === 0) return -1;
          if (a.unreadCount === 0 && b.unreadCount > 0) return 1;
          if (a.unreadCount > 0 && b.unreadCount > 0) {
            if (b.unreadCount !== a.unreadCount)
              return b.unreadCount - a.unreadCount;
          }
          const timeA = a.latestMessageTime,
            timeB = b.latestMessageTime;
          if (timeA && timeB) {
            if (timeA.includes(":") && timeB.includes(":"))
              return timeB.localeCompare(timeA);
            return timeA > timeB ? -1 : 1;
          } else if (timeA) return -1;
          else if (timeB) return 1;
          return 0;
        });
    },
    // pendingFriendRequests() {
    //   return this.friendRequests.filter((req) => req.status === "0"); //0 申请中
    // },
     pendingFriendRequests() {
    return this.friendRequests.filter(req => req.status === "0" && req.contactType === "0");
    },
  pendingGroupRequests() {
    return this.friendRequests.filter(req => req.status === "0" && req.contactType === "1");
  },
    pendingFriendRequestCount() {
      //   return this.pendingFriendRequests.length;
      return this.friendApplyCount;
    },
  },
  watch: {
    currentChat(newChat) {
      if (newChat) this.scrollToBottom();
    },
  },
  mounted() {
    initWs(this.$store.getters.token);
    onMessage(this.handleWsMessage);
  },
  beforeDestroy() {
    clearInterval(this.backgroundMessageInterval);
  },
  methods: {
    handleFriendRequests() {
      this.friendRequests = [];
      this.requestActiveTab = 'friendRequests'; // 默认显示好友申请标签页
      getFriendRequest({ id: this.myId }).then((res) => {
        if (res.data) {
          const friendRequestList = res.data;
          friendRequestList.forEach((friend) => {
            const newFriendRequest = {
              id: friend.id,
              senderId: friend.applyUserId,
              senderName: friend.applyUserName,
              senderAvatar: friend.avatar
                ? friend.avatar
                : friend.avatar.charAt(0),
              senderAvatarColor: getRandomColor(),
              message: friend.applyInfo,
              status: friend.status,
              contactType: friend.contactType, // 0: 好友申请, 1: 群申请
            groupId: friend.contactId,       // 如果是群申请，这是群ID
            groupName: friend.contactName   
            };
            this.friendRequests.push(newFriendRequest);
          });
        }
        this.friendRequestsDialogVisible = true;
      });
    },
    handleWsMessage(data) {
      let jsonData = JSON.parse(data);
      //发送好友请求后，接受人的好友申请数量+1
      if (jsonData.messageType === "4" && jsonData.concatType === "0") {
        this.friendApplyCount++;
      }
      //好友申请同意后的处理添加还有到列表
      if (jsonData.messageType === "1") {
        const newFriend = {
          id: jsonData.sendUserId,
          sessionId: jsonData.sessionId,
          name: jsonData.sendUserName,
          avatar:
            jsonData.concatId === "robot_001"
              ? "🤖"
              : jsonData.senderAvatar
              ? jsonData.senderAvatar
              : jsonData.sendUserName.charAt(0),
          avatarColor: getRandomColor(),
          remark: "好友添加",
          type: jsonData.concatType,
          isPinned: false,
        };
        this.friends.push(newFriend);
        this.$set(this.chatHistory, newFriend.sessionId, []);
        const formattedMsg = {
          msgId: jsonData.id,
          type: jsonData.messageType === "5" ? "media" : "text", // 简化处理为 text 类型
          sender: jsonData.concatId,
          content: jsonData.messageContent || "",
          time: new Date(jsonData.sendTime).toLocaleTimeString([], {
            year: "2-digit",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
          }),
        };
        this.chatHistory[jsonData.sessionId].push(formattedMsg);
      }
      //创建群聊后的通知
      if (jsonData.messageType === '3') {
        const newGroup = {
              id: jsonData.concatId,
              sessionId: jsonData.sessionId,
              name: jsonData.groupDto.name,
              avatar:
                jsonData.concatId === "robot_001"
                  ? "🤖"
                  : jsonData.groupDto.avatar
                  ? jsonData.groupDto.avatar
                  : jsonData.groupDto.name.charAt(0),
              avatarColor: getRandomColor(),
              remark: "系统初始化",
              members: jsonData.groupDto.members,//将成员放进去
              type: jsonData.concatType, //dm  0是好友 1是群聊
              isPinned: false,
        };
            newGroup.members.push(jsonData.groupDto.id)//将群主本人放到群聊中
            this.groups.push(newGroup);
        this.$set(this.chatHistory, newGroup.sessionId, []);
          const formattedMsg = {
            msgId: jsonData.id,
            type: jsonData.messageType === "5" ? "media" :jsonData.messageType === "3"||jsonData.messageType === "9"? "system" : "text", // 简化处理为 text 类型
            sender: null,
            content: jsonData.messageContent || "",
            time: new Date(jsonData.sendTime).toLocaleTimeString([], {
              year: "2-digit",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            }),
          };
          this.chatHistory[jsonData.sessionId].push(formattedMsg);
      }
      //成员进入群聊
      if (jsonData.messageType === '9') {
        // this.groups.forEach(group => {
        //   if(group.id === jsonData.concatId){
        //     group.members.push(jsonData.groupDto.id);
        //   }
        // })
        const formattedMsg = {
            msgId: jsonData.id,
            type: jsonData.messageType === "5" ? "media" :jsonData.messageType === "3"||jsonData.messageType === "9"? "system" : "text", // 简化处理为 text 类型
            sender: null,
            content: jsonData.messageContent || "",
            time: new Date(jsonData.sendTime).toLocaleTimeString([], {
              year: "2-digit",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            }),
        };
          this.chatHistory[jsonData.sessionId].push(formattedMsg);
      }
      //初始化
      if (jsonData.chatInitDto) {
        this.friends = []
        this.groups = []
        this.chatHistory={}
        this.friendApplyCount = jsonData.chatInitDto.applyCount;
        const { chatSessionUsers = [], chatMessages = [] } =
          jsonData.chatInitDto;
        // 添加 chatSessionUsers 中的用户为好友
        chatSessionUsers.forEach((user) => {
          const existing = this.friends.find((f) => f.id === user.concatId);
          if (!existing && user.concatType === "0") {
            const newFriend = {
              id: user.concatId,
              sessionId: user.sessionId,
              name: user.contactName,
              avatar:
                user.concatId === "robot_001"
                  ? "🤖"
                  : user.concatAvatar
                  ? user.concatAvatar
                  : user.contactName.charAt(0),
              avatarColor: getRandomColor(),
              remark: "系统初始化",
              type: user.concatType, //dm  0是好友 1是群聊
              isPinned: false,
            };
            this.friends.push(newFriend);
            this.$set(this.chatHistory, newFriend.sessionId, []);
          }
        });
        // 添加 chatSessionUsers 中的群组
        chatSessionUsers.forEach((user) => {
          // const existing =this.groups.find((f) => {
          //   f.id === user.concatId;
          // });
          const existing = this.groups.some((f) => 
            f.id === user.concatId)
          if (existing) {
            this.groups.forEach(f => {
              if (f.id === user.concatId&& user.concatType === "1") {
                f.members.push(user.userId);
              }
            })
          }
          if (!existing && user.concatType === "1") {
            const newGroup = {
              id: user.concatId,
              sessionId: user.sessionId,
              name: user.contactName,
              avatar:
                user.concatId === "robot_001"
                  ? "🤖"
                  : user.concatAvatar
                  ? user.concatAvatar
                  : user.contactName.charAt(0),
              avatarColor: getRandomColor(),
              remark: "系统初始化",
              members: [user.userId],
              type: user.concatType, //dm  0是好友 1是群聊
              isPinned: false,
            };
            this.groups.push(newGroup);
            this.$set(this.chatHistory, newGroup.sessionId, []);
          }
        });
        // 初始化 chatMessages
        chatMessages.forEach((msg) => {
          const senderId = msg.sendUserId;
          const chatId = msg.sessionId;
          if (!this.chatHistory[chatId]) {
            this.$set(this.chatHistory, chatId, []);
          }
          const formattedMsg = {
            msgId: msg.id,
            type: msg.messageType === "5" ? "media" :msg.messageType === "3"||msg.messageType === "9" ? "system" : "text", // 简化处理为 text 类型
            sender: senderId,
            content: msg.messageContent || "",
            time: new Date(msg.sendTime).toLocaleTimeString([], {
              year: "2-digit",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            }),
          };
          this.chatHistory[chatId].push(formattedMsg);
        });
        // this.$message.success("初始化聊天信息成功！");
      }
    },
    // ... (getSenderName, getSenderAvatarText, getSenderAvatarColor, handleHeaderCommand, Friend CRUD, Group CRUD, togglePinChat, handleSelectChat, handleDeleteCurrentChat, _addMessageToChat, _getBaseMessage, sendMessage methods, formatFileSize, getFileIcon, mockDownload, scrollToBottom as in previous step) ...
    getSenderName(senderId, defaultName = "用户") {
      if (senderId === this.myId) return this.myAvatar;
      const friend = this.friends.find((f) => f.id === senderId);
      return friend ? friend.name : defaultName;
    },
    getSenderAvatarText(senderId) {
      if (senderId === this.myId) return this.myAvatar[0] || "?";
      const friend = this.friends.find((f) => f.id === senderId);
      return friend ? friend.avatar || friend.name[0] || "?" : "?";
    },
    getSenderAvatarColor(senderId) {
      if (senderId === this.myId) return this.myAvatarColor;
      const friend = this.friends.find((f) => f.id === senderId);
      return friend ? friend.avatarColor || getRandomColor() : getRandomColor();
    },
    handleHeaderCommand(command) {
      if (command === "addFriend") this.handleAddFriendDialog();
      else if (command === "createGroup") this.handleCreateGroupDialog();
      // else if (command === "simulateRequest") this.simulateNewFriendRequest();
    },
    handleAddFriendDialog() {
      this.friendDialogTitle = "添加好友/入群";
      this.activeTab = 'addFriend'; // 默认显示添加好友标签页
      this.friendForm = {
        id: null,
        name: "",
        avatar: "",
        remark: "",
        avatarColor: getRandomColor(),
        searchName: "", // 重置搜索名称
        applyInfo: ""
      };
      this.searchResult = null; // 重置搜索结果
      this.showRemarkInput = false; // 重置备注输入框显示状态

      // 重置群组表单
    this.groupJoinForm = {
      searchGroupName: "",
      applyInfo: ""
    };
    // 重置群组表单
    // this.groupJoinForm = {
    //   searchGroupName: "",
    //   applyReason: ""
    // };
      this.friendDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.friendForm) this.$refs.friendForm.clearValidate();
      });
    },
    // 新增：搜索用户方法
    searchUser() {
      if (!this.friendForm.searchName.trim()) {
        this.$message.warning("请输入用户名");
        return;
      }

      this.searching = true;
      this.showRemarkInput = false; // 重新搜索时隐藏备注输入框

      getUserByAccountName({ accountName: this.friendForm.searchName })
        .then((response) => {
          if (response.data) {
            const userData = response.data;
            this.searchResult = {
              found: true,
              id: userData.id,
              name: userData.name,
              avatar: userData.avatar || userData.name[0],
              avatarColor: userData.avatarColor || getRandomColor(),
            };
          } else {
            this.searchResult = { found: false };
          }
          this.searching = false;
        })
        .catch((error) => {
          console.error("搜索用户失败:", error);
          this.searchResult = { found: false };
          this.searching = false;
          this.$message.error("搜索用户失败，请重试");
        });
    },
    // 新增：准备添加好友（显示备注输入框）
    prepareAddFriend() {
      if (!this.searchResult || !this.searchResult.found) {
        return;
      }

      this.showRemarkInput = true;
    },

    // 新增：取消添加好友
    cancelAddFriend() {
      if (this.showRemarkInput) {
        // 如果已经显示备注框，点击取消只是返回到搜索结果页面
        this.showRemarkInput = false;
        this.friendForm.applyInfo = "";
      } else {
        // 否则关闭整个对话框
        this.friendDialogVisible = false;
      }
    },
    // 新增：发送好友请求方法
    sendFriendRequest() {
      if (!this.searchResult || !this.searchResult.found) {
        return;
      }

      sendFriendRequest({
        receiveUserId: this.searchResult.id,
        applyUserId: this.$store.getters.id,
        applyInfo: this.friendForm.applyInfo,
        contactId: this.searchResult.id,
        contactType: "0",
        status: "0",
      })
        .then((response) => {
          if (response.data) {
            this.$message.success(
              `已向 ${this.searchResult.name} 发送好友请求`
            );
            this.friendDialogVisible = false;
          } else {
            this.$message.error(response.data.message || "发送请求失败");
          }
        })
        .catch((error) => {
          console.error("发送好友请求失败:", error);
          this.$message.error("发送好友请求失败，请重试");
        });
    },
    handleEditFriendDialog(friendId) {
      const friend = this.friends.find((f) => f.id === friendId);
      if (friend) {
        this.friendDialogTitle = "编辑好友";
        this.friendForm = {
          ...friend,
          avatarColor: friend.avatarColor || getRandomColor(),
        };
        this.friendDialogVisible = true;
        this.$nextTick(() => {
          if (this.$refs.friendForm) this.$refs.friendForm.clearValidate();
        });
      }
    },
    // submitFriendForm() {
    //   this.$refs.friendForm.validate((valid) => {
    //     if (valid) {
    //       const formToSubmit = { ...this.friendForm, type: "dm" };
    //       if (!formToSubmit.avatarColor)
    //         formToSubmit.avatarColor = getRandomColor();
    //       if (formToSubmit.id) {
    //         const index = this.friends.findIndex(
    //           (f) => f.id === formToSubmit.id
    //         );
    //         if (index !== -1) this.friends.splice(index, 1, formToSubmit);
    //         this.$message.success("好友信息更新成功！");
    //       } else {
    //         formToSubmit.id = `friend_${this.nextFriendIdSuffix++}`;
    //         if (!formToSubmit.avatar && formToSubmit.name)
    //           formToSubmit.avatar = formToSubmit.name[0];
    //         else if (!formToSubmit.avatar) formToSubmit.avatar = "?";
    //         this.friends.push(formToSubmit);
    //         if (!this.chatHistory[formToSubmit.id]) {
    //           this.$set(this.chatHistory, formToSubmit.id, []);
    //         }
    //         this.$message.success("好友添加成功！");
    //       }
    //       this.friendDialogVisible = false;
    //     }
    //   });
    // },
    handleCreateGroupDialog() {
      this.groupDialogTitle = "创建群组";
      this.groupForm = {
        id: this.myId,
        name: "",
        avatar: "",
        members: [],
        avatarColor: getRandomColor(),
        isPinned: false,
      };
      this.groupDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.groupForm) this.$refs.groupForm.clearValidate();
      });
    },
    handleEditGroupDialog(groupId) {
      const group = this.groups.find((g) => g.id === groupId);
      if (group) {
        this.groupDialogTitle = "编辑群组";
        this.groupForm = {
          ...group,
          avatarColor: group.avatarColor || getRandomColor(),
        };
        this.groupDialogVisible = true;
        this.$nextTick(() => {
          if (this.$refs.groupForm) this.$refs.groupForm.clearValidate();
        });
      }
    },
    // submitGroupForm() {
    //   this.$refs.groupForm.validate((valid) => {
    //     if (valid) {
    //       handleGroupRequest(this.groupForm).then((res) => {
    //         if (res.data) {
    //           this.$message.success("群组创建成功！");
    //           this.groupDialogVisible = false;
    //         }
    //       });
    //     }
    //   });
    // },
    // 修改提交群组表单的方法
    submitGroupForm() {
    this.$refs.groupForm.validate((valid) => {
    if (valid) {
      handleGroupRequest(this.groupForm).then((res) => {
        if (res.data) {
          this.$message.success("群组创建成功！");
          this.groupDialogVisible = false;
          
          // 获取新创建的群组ID
          const groupId = res.data.id || res.data;
          
          // 添加系统消息：创建时间和成员加入提示
          if (this.chatHistory[groupId]) {
            // 添加创建时间系统消息
            const createTimeMsg = {
              type: 'system',
              content: `群聊创建于 ${new Date().toLocaleString()}`,
              time: new Date().toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              }),
              msgId: Date.now()
            };
            this._addMessageToChat(groupId, createTimeMsg);
            
            // 添加创建者(自己)加入群聊的系统消息
            const creatorJoinMsg = {
              type: 'system',
              content: `${this.myAvatar || '您'} 创建并加入了群聊`,
              time: new Date().toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              }),
              msgId: Date.now() + 1
            };
            this._addMessageToChat(groupId, creatorJoinMsg);
            
            // 添加其他成员加入群聊的系统消息
            this.groupForm.members.forEach((memberId, index) => {
              const memberName = this.getSenderName(memberId);
              const memberJoinMsg = {
                type: 'system',
                content: `${memberName} 加入了群聊`,
                time: new Date().toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit'
                }),
                msgId: Date.now() + 2 + index
              };
              this._addMessageToChat(groupId, memberJoinMsg);
            });
          }
        }
      });
    }
  });
},
    togglePinChat(chatToToggle) {
      let itemToUpdate;
      if (chatToToggle.type === "dm")
        itemToUpdate = this.friends.find((f) => f.id === chatToToggle.id);
      else if (chatToToggle.type === "group")
        itemToUpdate = this.groups.find((g) => g.id === chatToToggle.id);
      if (itemToUpdate) {
        this.$set(itemToUpdate, "isPinned", !itemToUpdate.isPinned);
        if (this.currentChat && this.currentChat.chatId === itemToUpdate.id) {
          this.$set(this.currentChat, "isPinned", itemToUpdate.isPinned);
        }
      }
    },
    handleSelectChat(chatId) {
      const selectedChat = this.chatsWithDetails.find(
        (c) => c.chatId === chatId
      );
      if (selectedChat) {
        this.currentChat = selectedChat;
        this.newMessageText = "";
        if (this.unreadCounts[chatId] && this.unreadCounts[chatId] > 0)
          this.$set(this.unreadCounts, chatId, 0);
        if (!this.chatHistory[chatId]) this.$set(this.chatHistory, chatId, []);
        this.scrollToBottom();
      }
    },
    handleDeleteCurrentChat() {
      if (!this.currentChat) return;
      const chatName = this.currentChat.displayName;
      this.$confirm(
        `确定要删除会话 "${chatName}" 吗? 相关聊天记录也将清除。`,
        "提示",
        { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }
      )
        .then(() => {
          const chatId = this.currentChat.chatId;
          if (this.currentChat.type === "dm")
            this.friends = this.friends.filter((f) => f.id !== chatId);
          else if (this.currentChat.type === "group")
            this.groups = this.groups.filter((g) => g.id !== chatId);
          if (this.chatHistory[chatId]) this.$delete(this.chatHistory, chatId);
          if (this.unreadCounts[chatId])
            this.$delete(this.unreadCounts, chatId);
          this.currentChat = null;
          this.$message.success(`会话 "${chatName}" 删除成功!`);
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    _addMessageToChat(chatId, message) {
      if (!this.chatHistory[chatId]) this.$set(this.chatHistory, chatId, []);
      message.msgId = messageIdCounter++;
      this.chatHistory[chatId].push(message);
      if (this.currentChat && chatId === this.currentChat.chatId)
        this.scrollToBottom();
    },
    // _getBaseMessage(senderId) {
    //   return {
    //     sender: senderId,
    //     time: new Date().toLocaleTimeString([], {
    //       hour: "2-digit",
    //       minute: "2-digit",
    //       second: "2-digit",
    //     }),
    //   };
    // },
    _getBaseMessage(senderId, type = null) {
      return {
        sender: senderId,
        type: type || 'text',
        time: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
        }),
      };
    },
    sendTextMessage() {
      if (!this.newMessageText.trim() || !this.currentChat) return;
      const message = {
        ...this._getBaseMessage(this.myId),
        type: "text",
        content: this.newMessageText,
      };
      this._addMessageToChat(this.currentChat.chatId, message);
      // this.mockReply(this.currentChat, "text", this.newMessageText);
      this.newMessageText = "";
    },
    sendEmoji(emoji) {
      if (!this.currentChat) return;
      const message = {
        ...this._getBaseMessage(this.myId),
        type: "emoji",
        content: emoji,
      };
      this._addMessageToChat(this.currentChat.chatId, message);
      // this.mockReply(this.currentChat, "emoji", emoji);
      this.emojiPickerVisible = false;
    },
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    handleFileSelected(event) {
      if (!this.currentChat) return;
      const file = event.target.files[0];
      if (!file) return;
      const isImage = file.type.startsWith("image/");
      let previewUrl = null;
      const sendFileMsg = () => {
        const message = {
          ...this._getBaseMessage(this.myId),
          type: "file",
          file: {
            name: file.name,
            size: file.size,
            type: file.type,
            isImage,
            previewUrl,
          },
        };
        this._addMessageToChat(this.currentChat.chatId, message);
        // this.mockReply(this.currentChat, "file", file.name);
        this.$refs.fileInput.value = "";
      };
      if (isImage && file.size < 5 * 1024 * 1024) {
        const reader = new FileReader();
        reader.onload = (e) => {
          previewUrl = e.target.result;
          sendFileMsg();
        };
        reader.onerror = sendFileMsg;
        reader.readAsDataURL(file);
      } else {
        sendFileMsg();
      }
    },

    acceptFriendRequest(requestId, senderName) {
      const requestIndex = this.friendRequests.findIndex(
        (req) => req.id === requestId
      );
      handleFriendRequest({ applyId: requestId, status: "1" }).then((res) => {
        if (res.data) {
          this.$message.success(`已添加 ${senderName} 为好友！`);
          this.$set(this.friendRequests[requestIndex], "status", "accepted");
        }
      });
    },
    declineFriendRequest(requestId) {
      const requestIndex = this.friendRequests.findIndex(
        (req) => req.id === requestId
      );
      if (requestIndex > -1) {
        this.$set(this.friendRequests[requestIndex], "status", "declined");
        this.$message.info("已拒绝该好友请求。");
      }
    },

    formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },
    getFileIcon(fileName) {
      const ext = fileName.slice(fileName.lastIndexOf(".") + 1).toLowerCase();
      if (["jpg", "png", "gif"].includes(ext)) return "el-icon-picture-outline";
      return "el-icon-document";
    },
    mockDownload(file) {
      this.$message.info(`模拟下载: ${file.name}`);
    },
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.chatMessages)
          this.$refs.chatMessages.scrollTop =
            this.$refs.chatMessages.scrollHeight;
      });
    },
    // 搜索群组
  searchGroup() {
    if (!this.groupJoinForm.searchGroupName.trim()) {
      this.$message.warning("请输入群名称");
      return;
    }

    this.searchingGroup = true;
    this.showGroupRemarkInput = false;

    // 调用API搜索群组
    searchGroupByName(this.groupJoinForm.searchGroupName)
      .then(response => {
        if (response && response.data) {
          const groupData = response.data;
          this.groupSearchResult = {
            found: true,
            id: groupData.id,
            name: groupData.name,
            avatar: groupData.avatar || groupData.name[0],
            avatarColor: groupData.avatarColor || getRandomColor(),
            memberCount: groupData.memberCount || 0
          };
        } else {
          this.groupSearchResult = { found: false };
        }
        this.searchingGroup = false;
      })
      .catch(error => {
        console.error("搜索群组失败:", error);
        this.groupSearchResult = { found: false };
        this.searchingGroup = false;
        this.$message.error("搜索群组失败，请重试");
      });
  },
  // 准备加入群组（显示申请理由输入框）
  prepareJoinGroup() {
    if (!this.groupSearchResult || !this.groupSearchResult.found) {
      return;
    }
    this.showGroupRemarkInput = true;
  },
  // 取消加入群组
  cancelJoinGroup() {
    if (this.showGroupRemarkInput) {
      this.showGroupRemarkInput = false;
      this.groupJoinForm.applyInfo = '';
    } else {
      this.friendDialogVisible = false;
    }
  },
  // 发送入群申请
  sendGroupJoinRequest() {
    if (!this.groupSearchResult || !this.groupSearchResult.found) {
      return;
    }

    // 调用API发送入群申请
    sendFriendRequest({
      receiveUserId: this.groupSearchResult.id,
        applyUserId: this.$store.getters.id,
        applyInfo: this.groupJoinForm.applyInfo,
        contactId: this.groupSearchResult.id,
        contactType: "1",
        status: "0",
      // receiveUserId: this.groupSearchResult.id,
      // applyUserId: this.myId,
      // applyReason: this.groupJoinForm.applyReason,
      // status: "0" // 申请中
    })
      .then(response => {
        if (response.data) {
          this.$message.success(`已向 ${this.groupSearchResult.name} 发送入群申请`);
          this.friendDialogVisible = false;
        } else {
          this.$message.error(response.data.message || "发送申请失败");
        }
      })
      .catch(error => {
        console.error("发送入群申请失败:", error);
        this.$message.error("发送入群申请失败，请重试");
      });
    },
  // 接受入群请求
  acceptGroupRequest(requestId, senderName, groupId) {
    handleGroupRequest({ 
      applyId: requestId, 
      status: "1",
      groupId: groupId
    }).then((res) => {
      if (res.data) {
        this.$message.success(`已同意 ${senderName} 的入群申请！`);
        // 更新请求状态
        const requestIndex = this.friendRequests.findIndex(req => req.id === requestId);
        if (requestIndex > -1) {
          this.$set(this.friendRequests[requestIndex], "status", "1");
        }
      }
    });
  },
  
  // 拒绝入群请求
  declineGroupRequest(requestId) {
    handleGroupRequest({ 
      applyId: requestId, 
      status: "2" 
    }).then((res) => {
      if (res.data) {
        this.$message.info("已拒绝该入群申请");
        // 更新请求状态
        const requestIndex = this.friendRequests.findIndex(req => req.id === requestId);
        if (requestIndex > -1) {
          this.$set(this.friendRequests[requestIndex], "status", "2");
        }
      }
    });
  },
  },
};
</script>

<style>
/* ... (All previous styles) ... */
.header-actions-group {
  display: flex;
  align-items: center;
}
.header-action-badge .el-badge__content {
  transform: translate(50%, -30%);
} /* Adjust badge on header button */

.request-card {
  margin-bottom: 15px;
}
.request-item {
  display: flex;
  align-items: center;
}
.request-info {
  flex-grow: 1;
  margin-right: 15px;
}
.request-sender-name {
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}
.request-message {
  font-size: 0.9em;
  color: #555;
  margin: 0;
}
.request-message-empty {
  font-size: 0.9em;
  color: #999;
  margin: 0;
  font-style: italic;
}
.request-actions .el-button {
  margin-left: 10px;
}
.no-requests {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

/* Ensure all other styles from previous steps are present */
/* General App Styles */
.chat-app {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  background-color: #f4f5f7;
}
/* Friend List Area */
.chat-list-aside {
  border-right: 1px solid #e0e0e0;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}
.chat-list-header {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}
.chat-list-header h2 {
  margin: 0;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}
.chat-el-menu {
  border-right: none;
  flex-grow: 1;
  overflow-y: auto;
}
.no-chats-item {
  padding: 0 20px;
  line-height: 56px;
  text-align: center;
  color: #909399;
}
.chat-list-item-padding {
  height: auto !important;
  padding: 12px 10px 12px 15px !important;
  line-height: normal !important;
  display: flex !important;
  align-items: center !important;
  transition: background-color 0.2s ease;
}
.chat-list-item-padding:hover {
  background-color: #f5f5f5 !important;
}
.el-menu-item.is-active.chat-list-item-padding {
  background-color: #e8f0ff !important;
}
.chat-list-item-padding.pinned-chat-item {
  /* background-color: #fffbeb; */
}
.chat-item-detailed {
  display: flex;
  align-items: center;
  width: 100%;
}
.chat-list-badge {
  margin-right: 12px;
  display: flex;
  align-items: center;
}
.chat-list-badge .el-badge__content {
  transform: translateY(-2px) translateX(100%);
  font-size: 10px;
  line-height: 16px;
  height: 16px;
  min-width: 16px;
  padding: 0 5px;
}
.chat-list-badge .el-badge__content.is-dot {
  height: 8px;
  width: 8px;
  min-width: 8px;
  right: 2px;
  top: 2px;
}
.chat-avatar {
  flex-shrink: 0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-radius: 8px;
}
.chat-avatar .el-icon-user-solid {
  font-size: 20px;
}
.chat-info {
  flex-grow: 1;
  overflow: hidden;
  margin-right: 5px;
}
.chat-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
}
.chat-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}
.latest-message-time {
  font-size: 11px;
  color: #888;
  white-space: nowrap;
  margin-left: 8px;
}
.latest-message-text {
  font-size: 13px;
  color: #555;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.emoji-snippet {
  font-size: 14px;
}
.pin-chat-button {
  margin-left: auto;
  padding: 5px !important;
}
.pin-chat-button.el-button.is-plain[type="warning"] {
  background: #fffbeb;
  border-color: #f8d38a;
  color: #e6a23c;
}
.pin-chat-button.el-button.is-plain[type="warning"]:hover {
  background: #e6a23c;
  color: white;
}
/* Chat Area */
.chat-main {
  padding: 0;
  display: flex;
  flex-direction: column;
  background-color: #f4f5f7;
}
.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.chat-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}
.chat-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}
.chat-header h3 .group-member-count {
  font-size: 0.8em;
  color: #909399;
  margin-left: 5px;
  font-weight: normal;
}
.pinned-chat-indicator {
  color: #e6a23c;
  margin-left: 8px;
  font-size: 1em;
}
.chat-header-actions .el-button {
  margin-left: 10px;
}
/* Chat Messages Display */
.chat-messages {
  flex-grow: 1;
  padding: 20px 20px 10px 20px;
  overflow-y: auto;
}
.message-item {
  display: flex;
  margin-bottom: 18px;
  align-items: flex-end;
  max-width: 85%;
}
.received-item {
  justify-content: flex-start;
  margin-right: auto;
}
.sent-item {
  justify-content: flex-end;
  margin-left: auto;
}
.message-avatar {
  flex-shrink: 0;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.received-item .message-avatar {
  margin-right: 10px;
}
.sent-item .message-avatar {
  margin-left: 10px;
  order: 1;
}
.message-bubble-wrapper {
  display: flex;
  flex-direction: column;
}
.received-item .message-bubble-wrapper {
  align-items: flex-start;
}
.sent-item .message-bubble-wrapper {
  align-items: flex-end;
}
.message-sender-name {
  font-size: 0.75em;
  color: #888;
  margin-bottom: 2px;
  margin-left: 2px;
}
.message-bubble {
  padding: 10px 14px;
  border-radius: 12px;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  position: relative;
  max-width: calc(100% - 0px);
}
.message-bubble.sent:not(.message-type-emoji) {
  background-color: #007bff;
  color: white;
  border-bottom-right-radius: 5px;
}
.message-bubble.received:not(.message-type-emoji) {
  background-color: #ffffff;
  color: #333;
  border: 1px solid #e9e9eb;
  border-bottom-left-radius: 5px;
}
.message-text {
  margin: 0 0 4px 0;
  line-height: 1.5;
  font-size: 14px;
  white-space: pre-wrap;
}
.message-time {
  font-size: 10px;
  display: block;
  color: #aaa;
}
.message-bubble.sent .message-time {
  text-align: right;
  color: rgba(255, 255, 255, 0.7);
}
.message-bubble.received .message-time {
  text-align: left;
}
.message-bubble.sent.message-type-emoji .message-time,
.message-bubble.received.message-type-emoji .message-time {
  color: #999;
}
.no-messages,
.no-chat-selected {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #aaa;
  font-size: 16px;
  text-align: center;
}
.no-chat-selected::before {
  content: "💬";
  font-size: 48px;
  margin-bottom: 10px;
}
/* Chat Input Area */
.chat-input-area {
  display: flex;
  align-items: flex-end;
  padding: 10px 15px;
  border-top: 1px solid #e0e0e0;
  background-color: #ffffff;
  flex-shrink: 0;
}
.chat-actions {
  display: flex;
  align-items: center;
  margin-right: 10px;
}
.chat-actions .el-button {
  margin-right: 5px;
  padding: 8px;
}
.chat-input-area .text-input {
  flex-grow: 1;
  margin-right: 10px;
}
.chat-input-area .text-input textarea {
  border-radius: 18px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
}
.chat-input-area .send-button {
  min-width: 70px;
  border-radius: 18px;
  font-weight: 500;
  height: 38px;
}
/* Emoji Picker */
.emoji-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 150px;
  overflow-y: auto;
  padding: 5px;
}
.emoji-char {
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
}
.emoji-char:hover {
  background-color: #f0f0f0;
}
/* Message Bubbles - Type Specific */
.message-bubble.message-type-emoji {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}
.message-emoji {
  font-size: 28px;
  line-height: 1;
}
.message-file {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 8px;
  gap: 10px;
  max-width: 300px;
}
.message-bubble.sent .message-file {
  background-color: rgba(255, 255, 255, 0.2);
}
.message-bubble.received .message-file {
  background-color: #e9ecef;
}
.file-icon {
  font-size: 28px;
  color: #606266;
}
.message-bubble.sent .file-icon {
  color: #f0f0f0;
}
.file-image-preview {
  max-width: 150px;
  max-height: 100px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
}
.file-info {
  flex-grow: 1;
  overflow: hidden;
  font-size: 13px;
}
.file-name {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}
.message-bubble.sent .file-name {
  color: #fff;
}
.message-bubble.received .file-name {
  color: #333;
}
.file-size {
  font-size: 11px;
  color: #888;
}
.message-bubble.sent .file-size {
  color: #e0e0e0;
}
.file-download-button.el-button--text {
  padding: 0;
  font-size: 12px;
}
.message-bubble.sent .file-download-button.el-button--text {
  color: #cce5ff;
}
.message-bubble.sent .file-download-button.el-button--text:hover {
  color: #fff;
}
/* Scrollbar Styling */
.friend-el-menu::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar,
.emoji-picker::-webkit-scrollbar,
.chat-el-menu::-webkit-scrollbar {
  width: 6px;
}
.friend-el-menu::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb,
.emoji-picker::-webkit-scrollbar-thumb,
.chat-el-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}
.friend-el-menu::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover,
.emoji-picker::-webkit-scrollbar-thumb:hover,
.chat-el-menu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* 添加这些样式来移除空白 */
.full-height {
  height: 100%;
}

.no-padding {
  padding: 0 !important;
}

.chat-app {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  background-color: #f4f5f7;
  display: flex;
  flex-direction: column;
}

.chat-main {
  padding: 0;
  display: flex;
  flex-direction: column;
  background-color: #f4f5f7;
}

/* 保留原有的样式... */

.search-user-container {
  display: flex;
  gap: 10px;
}

.search-result {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.user-found {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: bold;
  font-size: 16px;
}

.user-not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #909399;
  padding: 10px 0;
}

.user-not-found i {
  font-size: 20px;
}

/* 添加系统消息样式 */
.system-message-item {
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.system-message {
  background-color: #f2f2f2;
  border-radius: 12px;
  padding: 4px 12px;
  font-size: 12px;
  color: #666;
  display: inline-flex;
  align-items: center;
}

.system-message-content {
  margin-right: 5px;
}
/* 添加新的样式 */
.search-result {
  margin: 15px 0;
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.user-found {
  display: flex;
  align-items: center;
}

.user-info {
  margin-left: 15px;
}

.user-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
}

.group-member-count {
  font-size: 12px;
  color: #606266;
}

.user-not-found {
  color: #f56c6c;
  text-align: center;
  padding: 10px;
}
/* 添加新的样式 */
.request-group-name {
  display: block;
  font-size: 13px;
  color: #606266;
  margin: 5px 0;
}

.no-requests {
  text-align: center;
  color: #909399;
  padding: 20px;
  font-size: 14px;
}

.request-card {
  margin-bottom: 15px;
}

.request-item {
  display: flex;
  align-items: flex-start;
}

.request-info {
  flex-grow: 1;
  margin-right: 15px;
}

.request-sender-name {
  font-weight: bold;
  font-size: 15px;
}

.request-message {
  margin: 5px 0;
  color: #606266;
  font-size: 13px;
}

.request-message-empty {
  margin: 5px 0;
  color: #909399;
  font-size: 13px;
  font-style: italic;
}

.request-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
</style>
