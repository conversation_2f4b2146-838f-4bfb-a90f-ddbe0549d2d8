<template>
  <div class="activation-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-section">
      <div class="loader" />
      <p>正在激活账户...</p>
    </div>

    <!-- 激活结果 -->
    <div v-else>
      <!-- 成功状态 -->
      <div v-if="activationStatus === 0" class="activation-success">
        <h2>✅ 激活成功</h2>
        <p>
          邮箱 <span class="highlight">{{ email }}</span> {{ activeMessage }}
        </p>
        <router-link to="/login" class="success-link"> 立即登录 </router-link>
      </div>

      <!-- 失败状态 -->
      <div v-else class="activation-fail">
        <h2>❌ 激活失败</h2>
        <p>
          邮箱 <span class="highlight">{{ email }}</span> 激活失败
        </p>
        <p class="error-message">错误原因：{{ errorMessage }}</p>
        <!-- <button
          :disabled="isRetrying"
          class="retry-btn"
          @click="retryActivation"
        >
          {{ isRetrying ? "正在重试..." : "重新尝试激活" }}
        </button> -->
      </div>
    </div>
  </div>
</template>

<script>
import { activeAccount } from "@/api/login";

export default {
  data() {
    return {
      loading: true,
      email: "",
      username: "",
      activationStatus: null,
      activeMessage: "",
      errorMessage: "",
      isRetrying: false,
    };
  },

  created() {
    // 从URL获取email参数
    this.email = this.$route.query.email;
    this.username = this.$route.query.username;
    if (!this.validateEmail(this.email)) {
      this.handleError("无效的邮箱地址");
      return;
    }

    this.sendActivationRequest();
  },

  methods: {
    validateEmail(email) {
      const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return re.test(email);
    },

    async sendActivationRequest() {
      try {
        // 调用你的真实API端点
        activeAccount({ email: this.email, username: this.username }).then(
          (res) => {
            if (res.data.status === 0) {
              this.activationStatus = 0;
              this.activeMessage = res.data.message;
            } else {
              this.handleError(res.data.message || "未知错误");
            }
          }
        );
        // const response = await this.$http.post("/api/activate", {
        //   email: this.email,
        // });

        // if (response.data.status === 0) {
        //   this.activationStatus = 0;
        // } else {
        //   this.handleError(response.data.message || "未知错误");
        // }
      } catch (error) {
        this.handleError(this.getErrorMessage(error));
      } finally {
        this.loading = false;
      }
    },

    getErrorMessage(error) {
      if (error.response) {
        switch (error.response.status) {
          case 400:
            return "请求参数错误";
          case 404:
            return "邮箱未注册";
          case 409:
            return "账户已激活";
          default:
            return "服务器错误";
        }
      }
      return "网络连接失败";
    },

    handleError(message) {
      this.activationStatus = 1;
      this.errorMessage = message;
    },

    async retryActivation() {
      this.isRetrying = true;
      await this.sendActivationRequest();
      this.isRetrying = false;
    },
  },
};
</script>

<style scoped>
.activation-container {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  text-align: center;
}

.loading-section {
  color: #666;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin: 0 auto 1rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.activation-success {
  color: #2ecc71;
}

.activation-fail {
  color: #e74c3c;
}

.highlight {
  font-weight: bold;
  color: #2980b9;
}

.success-link {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #3498db;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}

.error-message {
  margin: 1rem 0;
  color: #c0392b;
}

.retry-btn {
  padding: 0.5rem 1rem;
  background: #e67e22;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.retry-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
}

.retry-btn:hover:not(:disabled) {
  background: #d35400;
}
</style>
