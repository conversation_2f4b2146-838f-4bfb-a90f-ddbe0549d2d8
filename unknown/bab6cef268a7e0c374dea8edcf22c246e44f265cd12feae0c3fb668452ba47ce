// 用户模块 API 接口
// 该模块提供登录、注册、获取用户详情、退出登录以及验证码相关接口
import request from "@/utils/request"; // 引入封装好的 axios 实例
/**
登录接口
@param {string} username - 用户名
@param {string} password - 密码
@param {string} code - 验证码
@param {string} uuid - 唯一标识符，用于关联验证码等
@returns {Promise} 返回一个 Promise 对象，封装登录请求的结果
*/
export function login(username, password) {
  const data = { username, password };
  return request({
    url: "/login",
    method: "post",
    // 表示此请求不需要携带 token
    headers: { isToken: false },
    data: data,
  });
}
/**
注册接口
@param {Object} data - 用户注册所需数据（包含用户名、密码、邮箱等字段，根据实际情况调整）
@returns {Promise} 返回一个 Promise 对象，封装注册请求的结果
*/
export function register(data) {
  return request({
    url: "/users/register",
    method: "post",
    headers: { isToken: false },
    data: data,
  });
}
/**
获取用户详情接口
@returns {Promise} 返回一个 Promise 对象，封装获取用户信息的请求结果
*/
export function getInfo() {
  return request({
    url: "/users/getInfo",
    method: "get",
  });
}
/**
退出登录接口
@returns {Promise} 返回一个 Promise 对象，封装退出登录的请求结果
*/
export function logout() {
  return request({
    url: "/logout",
    method: "post",
  });
}
/**
发送邮件验证码接口
@param {Object} query - 请求参数（例如 { email: '<EMAIL>' }），根据实际 API 要求传递
@returns {Promise} 返回一个 Promise 对象，封装发送邮件验证码请求的结果
*/
export function sendEmailCode(query) {
  return request({
    url: "/system/sendEmailCode",
    method: "get",
    params: query,
  });
}
/**
 * 验证验证码
 */
export function validateEmailCode(data) {
  return request({
    url: "/system/validateEmailCode",
    method: "post",
    data: data,
  });
}

/**
 * 修改账号密码
 */
export function updateAccountPassword(data) {
  return request({
    url: "/users/updateAccountPassword",
    method: "post",
    headers: { isToken: false },
    data: data,
  });
}

/**
 * 激活账号
 */
export function activeAccount(params) {
  return request({
    url: "/users/activeAccount",
    method: "get",
    headers: { isToken: false },
    params: params,
  });
}
