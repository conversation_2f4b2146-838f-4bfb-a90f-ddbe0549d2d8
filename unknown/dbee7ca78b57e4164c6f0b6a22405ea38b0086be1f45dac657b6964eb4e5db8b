<template>
  <el-card class="social-account">
    <div slot="header">
      <span>第三方账户绑定</span>
    </div>

    <el-table :data="bindings">
      <el-table-column prop="provider" label="平台">
        <template slot-scope="{ row }">
          <svg-icon :icon-class="row.provider" />
          {{ providerMap[row.provider] }}
        </template>
      </el-table-column>

      <el-table-column label="操作">
        <template slot-scope="{ row }">
          <el-button
            v-if="row.bound"
            type="danger"
            @click="unbind(row.provider)"
          >
            解绑
          </el-button>
          <el-button v-else type="primary" @click="bind(row.provider)">
            绑定
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script>
export default {
  data() {
    return {
      providerMap: {
        wechat: "微信",
        github: "GitHub",
      },
      bindings: [
        { provider: "wechat", bound: false },
        { provider: "github", bound: true },
      ],
    };
  },

  methods: {
    bind(provider) {
      this.startAuthFlow(provider, "bind");
    },

    unbind(provider) {
      this.$confirm("确定要解绑该账户吗？", "提示").then(() => {
        // 调用解绑接口
      });
    },
  },
};
</script>
