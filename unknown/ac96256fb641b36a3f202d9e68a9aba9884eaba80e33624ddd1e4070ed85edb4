/**
权限模块 Vuex 配置
主要作用：
通过调用后端接口获取路由数据；
将后端返回的路由数据转换为前端路由配置；
添加动态路由、侧边栏路由、顶栏路由等； 
根据用户权限过滤动态路由。
*/
// import auth from "@/plugins/auth";
// import router, { constantRoutes, dynamicRoutes } from "@/router";
import { constantRoutes } from "@/router";
import { getRouters } from "@/api/menu";
import Layout from "@/layout/index";
import ParentView from "@/components/ParentView";
import InnerLink from "@/layout/components/InnerLink";

// Vuex 模块：权限
const permission = {
  state: {
    routes: [], // 最终生成的路由（包含常量路由和动态添加的路由）
    addRoutes: [], // 仅动态添加的路由配置
    defaultRoutes: [], // 默认显示的侧边栏路由
    topbarRouters: [], // 顶部导航路由
    sidebarRouters: [], // 侧边栏显示的路由
  },
  mutations: {
    // 设置动态添加的路由和最终路由集合
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes;
      state.routes = constantRoutes.concat(routes);
    },
    // 设置默认侧边栏路由
    SET_DEFAULT_ROUTES: (state, routes) => {
      state.defaultRoutes = constantRoutes.concat(routes);
    },
    // 设置顶部导航路由
    SET_TOPBAR_ROUTES: (state, routes) => {
      state.topbarRouters = routes;
    },
    // 设置侧边栏路由
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      state.sidebarRouters = routes;
    },
  },
  actions: {
    /**
     * 生成路由（通过后端获取路由数据，转换后交由 Vue Router 动态添加）
     *
     * @returns {Promise} 返回转换后的路由数组（rewriteRoutes）
     */
    async GenerateRoutes({ commit }) {
      // 从后端获取路由数据
      const res = await getRouters();
      // 这里通过深拷贝保证对原数据不产生直接修改
      const sdata = JSON.parse(JSON.stringify(res.data));
      const rdata = JSON.parse(JSON.stringify(res.data));
      let sidebarRoutes = [];
      let rewriteRoutes = [];
      if (res.data) {
        // 将后端传来的路由数据转换成前端路由配置（侧边栏）
        sidebarRoutes = filterAsyncRouter(sdata);
        // rewriteRoutes 为将要动态添加到 Vue Router 中的路由列表，此处对部分路由进行特殊处理
        rewriteRoutes = filterAsyncRouter(rdata, false, true);
      }
      // 对动态路由进行权限验证过滤
      // const asyncRoutes = filterDynamicRoutes(dynamicRoutes);
      // 添加404兜底路由（注意：在 Vue Router 3 中可用 router.addRoutes，如使用新版 Vue Router 4 请使用 router.addRoute）
      rewriteRoutes.push({ path: "*", redirect: "/404", hidden: true });

      // 动态添加权限路由（这里使用 addRoutes 方法，Vue Router 4 中建议替换为循环 router.addRoute）
      // router.addRoutes(asyncRoutes);
      // for (const route of asyncRoutes) {
      //   router.addRoute(route);
      // }

      // 通过 mutations 存储转换后的各种路由数据
      commit("SET_ROUTES", rewriteRoutes);
      commit("SET_SIDEBAR_ROUTERS", constantRoutes.concat(sidebarRoutes));
      commit("SET_DEFAULT_ROUTES", sidebarRoutes);
      commit("SET_TOPBAR_ROUTES", sidebarRoutes);

      return rewriteRoutes;
    },
  },
};

/**
遍历后端传来的路由配置信息，将字符串组件转换为真实的组件对象
@param {Array} asyncRouterMap - 后端传过来的路由列表
@param {Boolean|Object} [lastRouter=false] - 用于子路由递归时拼接路径时传入上级路由对象或标记，默认为 false
@param {Boolean} [type=false] - 是否启用 children 的特殊处理（只在部分根路由处理中使用）
@returns {Array} 返回转换后的路由配置
*/
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter((route) => {
    // 如果 type 为 true 并且该路由存在 children，则对子路由进行过滤处理
    if (type && route.children) {
      route.children = filterChildren(route.children);
    }
    if (route.component) {
      // 对特殊组件名称作处理：Layout、ParentView、InnerLink
      if (route.component === "Layout") {
        route.component = Layout;
      } else if (route.component === "ParentView") {
        route.component = ParentView;
      } else if (route.component === "InnerLink") {
        route.component = InnerLink;
      } else {
        // 其他组件使用 loadView 方法动态加载
        route.component = loadView(route.component);
      }
    }
    // 如果存在 children，则递归处理子路由，否则删除无用属性
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type);
    } else {
      delete route["children"];
      delete route["redirect"];
    }
    return true;
  });
}

/**
递归拼接子路由的路径
@param {Array} childrenMap - 子路由列表
@param {Object|Boolean} [lastRouter=false] - 如果传入对象则表示上级路由信息
@returns {Array} 返回处理后的子路由列表
*/
function filterChildren(childrenMap, lastRouter = false) {
  let children = [];
  childrenMap.forEach((el) => {
    if (el.children && el.children.length) {
      // 如果组件为 ParentView 且不处于最后一级，则对子路由进行路径拼接处理
      if (el.component === "ParentView" && !lastRouter) {
        el.children.forEach((c) => {
          c.path = el.path + "/" + c.path;
          // 如果子路由还有下一级，则递归处理
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c));
          } else {
            children.push(c);
          }
        });
        return; // 结束当前循环，进入下一个元素
      }
    }
    // 如果是最后一级路由，则拼接上级路由路径
    if (lastRouter) {
      el.path = lastRouter.path + "/" + el.path;
    }
    children = children.concat(el);
  });
  return children;
}

/**
根据权限配置过滤动态路由
@param {Array} routes - 动态路由列表
@returns {Array} 返回过滤后的路由列表，只有满足权限条件的路由会保留
*/
// export function filterDynamicRoutes(routes) {
//   const res = [];
//   routes.forEach((route) => {
//     if (route.permissions) {
//       // 若配置了权限字段，则调用 auth.hasPermiOr 判断当前用户是否有权限
//       if (auth.hasPermiOr(route.permissions)) {
//         res.push(route);
//       }
//     } else if (route.roles) {
//       // 若配置了角色字段，则调用 auth.hasRoleOr 判断当前用户是否拥有任一合法角色
//       if (auth.hasRoleOr(route.roles)) {
//         res.push(route);
//       }
//     }
//   });
//   return res;
// }

/**
动态加载视图组件
@param {string} view - 组件路径（对应 src/views 下的路径）
@returns {Function} 返回一个函数用于按需加载组件
*/
export const loadView = (view) => {
  if (process.env.NODE_ENV === "development") {
    // 开发环境下采用 require 确保热重载正常工作
    return (resolve) => require([`@/views/${view}`], resolve);
  } else {
    // 生产环境下使用 import 实现路由懒加载
    return () => import(`@/views/${view}`);
  }
};
export default permission;
