import Vue from "vue";
import Vuex from "vuex";

import user from "./modules/user";
import getters from "./getters";
import permission from "./modules/permission";
import settings from "./modules/settings";
import tagsView from "./modules/tagsView";
import app from "./modules/app";

Vue.use(Vuex);

export default new Vuex.Store({
  //state:仓库存储数据的地方
  state: {
    // user: null,
    // menus: [],
    // token: localStorage.getItem("token") || "",
    // roles: [],
    // dynamicRoutes: [], // 动态路由
  },
  getters,
  //action:处理action，可以书写自己的业务逻辑，也可以处理异步
  actions: {
    // async generateDynamicRoutes(menus) {
    //   // 根据菜单数据生成路由配置
    //   const routes = [];
    //   menus.forEach((menu) => {
    //     if (menu.path && menu.name) {
    //       const route = {
    //         path: menu.path,
    //         name: menu.name,
    //         component: () => import(`@/views/${menu.component}.vue`),
    //         meta: {
    //           requiresAuth: true,
    //           menu: menu,
    //         },
    //       };
    //       routes.push(route);
    //     }
    //   });
    //   return routes;
    // },
  },
  modules: {
    user,
    permission,
    settings,
    tagsView,
    app,
  },
});
