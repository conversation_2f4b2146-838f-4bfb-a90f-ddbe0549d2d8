{"name": "open-world", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@iconify/iconify": "^3.1.1", "axios": "^1.7.9", "compression-webpack-plugin": "^11.1.0", "core-js": "^3.8.3", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "fuse.js": "^7.1.0", "gsap": "^3.12.7", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "particles.js": "^2.0.0", "path": "^0.12.7", "qs": "^6.14.0", "screenfull": "^6.0.2", "vue": "^2.6.14", "vue-particles": "^1.0.9", "vue-router": "^3.5.1", "vue-social-auth": "^1.4.9", "vuex": "^3.6.2", "vue-meta": "^2.4.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-vue": "^8.0.3", "html-webpack-plugin": "^4.5.2", "path-browserify": "^1.0.1", "process": "^0.11.10", "sass": "^1.85.0", "sass-loader": "^16.0.5", "script-ext-html-webpack-plugin": "^2.1.5", "svg-sprite-loader": "^6.0.11", "svgo": "^3.0.2", "vue-template-compiler": "^2.6.14", "webpack": "^5.99.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}